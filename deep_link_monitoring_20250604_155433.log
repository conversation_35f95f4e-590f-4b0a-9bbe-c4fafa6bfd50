[2025-06-04 15:54:35] 06-04 20:20:35.734  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/1e5b568ae0b66f44_0
[2025-06-04 15:54:35] 06-04 20:20:35.735  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/d632eac984d4f85a_0
[2025-06-04 15:54:36] 06-04 20:20:35.735  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/6355db7246601511_0
[2025-06-04 15:54:36] 06-04 20:20:35.735  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/3a749abbb50dd088_0
[2025-06-04 15:54:36] 06-04 20:20:35.736  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/885292f3149edc23_0
[2025-06-04 15:54:36] 06-04 20:20:35.737  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/b7f8f25e1d22a660_0
[2025-06-04 15:54:37] 06-04 20:20:35.738  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/6b881cc0cf5efa49_0
[2025-06-04 15:54:37] 06-04 20:20:35.739  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/6132f9d01c91fc69_0
[2025-06-04 15:54:37] 06-04 20:20:35.739  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/c7215f54f4b23e4a_0
[2025-06-04 15:54:38] 06-04 20:20:35.740  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/6e5ba0e4e40e7af7_0
[2025-06-04 15:54:38] 06-04 20:20:35.741  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/41d84d812757edda_0
[2025-06-04 15:54:38] 06-04 20:20:35.742  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/99fdc680f35378ab_0
[2025-06-04 15:54:39] 06-04 20:20:35.748  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/9a2dc3d71f59e1b1_0
[2025-06-04 15:54:39] 06-04 20:20:35.748  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/6420e229a0d32f70_0
[2025-06-04 15:54:39] 06-04 20:20:35.750  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/15af9c849a9e2128_0
[2025-06-04 15:54:40] 06-04 20:20:35.752  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/605b00628408724d_0
[2025-06-04 15:54:40] 06-04 20:20:35.754  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/0aab3a6a40500ccd_0
[2025-06-04 15:54:40] 06-04 20:20:35.755  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/f8996b8880e6063f_0
[2025-06-04 15:54:41] 06-04 20:20:35.757  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/3f411a87584195f5_0
[2025-06-04 15:54:41] 06-04 20:20:35.758  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/2f2eee25f9d3c17f_0
[2025-06-04 15:54:41] 06-04 20:20:35.770  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/bac7d7467b950460_0
[2025-06-04 15:54:42] 06-04 20:20:35.771  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/0872eac81da71a32_0
[2025-06-04 15:54:42] 06-04 20:20:35.774  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/24c3ef698a7ee4ab_0
[2025-06-04 15:54:42] 06-04 20:20:35.775  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/a5623bdc6bbb3962_0
[2025-06-04 15:54:43] 06-04 20:20:35.776  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/0f8801795c02e7eb_0
[2025-06-04 15:54:43] 06-04 20:20:35.779  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/c8af002deff42b5a_0
[2025-06-04 15:54:43] 06-04 20:20:35.780  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/8baca1e87e1e4c27_0
[2025-06-04 15:54:44] 06-04 20:20:35.791  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/13842b395e63cf75_0
[2025-06-04 15:54:44] 06-04 20:20:35.791  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/7c0b9d56e36423dc_0
[2025-06-04 15:54:44] 06-04 20:20:35.792  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/254510303b0f3e67_0
[2025-06-04 15:54:45] 06-04 20:20:35.793  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/2827e515dffe55eb_0
[2025-06-04 15:54:45] 06-04 20:20:35.795  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/45a93d30d88761a8_0
[2025-06-04 15:54:45] 06-04 20:20:35.796  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/abc75a81b00bbe69_0
[2025-06-04 15:54:45] 06-04 20:20:35.798  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/ca240a22e42b0ffb_0
[2025-06-04 15:54:46] 06-04 20:20:35.799  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/56201d4b385c4690_0
[2025-06-04 15:54:46] 06-04 20:20:35.800  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/52fd0f847a5cf287_0
[2025-06-04 15:54:46] 06-04 20:20:35.808  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/e832c4a911de61d7_0
[2025-06-04 15:54:47] 06-04 20:20:35.811  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/057f32a5b875fe64_0
[2025-06-04 15:54:47] 06-04 20:20:35.816  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/e9b9805ee99dcf28_0
[2025-06-04 15:54:47] 06-04 20:20:35.820  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/6a15e8627f11cff2_0
[2025-06-04 15:54:48] 06-04 20:20:35.822  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(58)] Could not get file info for /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js/a81c4c476c2cf345_0
[2025-06-04 15:54:48] 06-04 20:20:35.823  7808  7854 E chromium: [ERROR:simple_file_enumerator.cc(43)] readdir /data/user/0/com.grupoavaloc1.bancamovil/cache/WebView/Default/HTTP Cache/Code Cache/js: No such file or directory (2)
[2025-06-04 15:54:48] 06-04 20:20:35.826  7808  7854 E chromium: [ERROR:simple_index_file.cc(614)] Could not reconstruct index from disk
[2025-06-04 15:54:49] 06-04 20:20:35.878  7808  7808 D Capacitor: Starting BridgeActivity
[2025-06-04 15:54:49] 06-04 20:20:35.897  7808  7808 D Capacitor: Registering plugin instance: CapacitorCookies
[2025-06-04 15:54:49] 06-04 20:20:35.903  7808  7808 D Capacitor: Registering plugin instance: WebView
[2025-06-04 15:54:50] 06-04 20:20:35.905  7808  7808 D Capacitor: Registering plugin instance: CapacitorHttp
[2025-06-04 15:54:50] 06-04 20:20:35.906  7808  7808 D Capacitor: Registering plugin instance: OnespanBinding
[2025-06-04 15:54:50] 06-04 20:20:35.908  7808  7808 D Capacitor: Registering plugin instance: OneSpanDigipass
[2025-06-04 15:54:50] 06-04 20:20:35.910  7808  7808 D Capacitor: Registering plugin instance: OneSpanSecureStorage
[2025-06-04 15:54:51] 06-04 20:20:35.911  7808  7808 D Capacitor: Registering plugin instance: AppReview
[2025-06-04 15:54:51] 06-04 20:20:35.913  7808  7808 D Capacitor: Registering plugin instance: BarcodeScanner
[2025-06-04 15:54:51] 06-04 20:20:35.915  7808  7808 D Capacitor: Registering plugin instance: Contacts
[2025-06-04 15:54:52] 06-04 20:20:35.917  7808  7808 D Capacitor: Registering plugin instance: DeviceManager
[2025-06-04 15:54:52] 06-04 20:20:35.919  7808  7808 D Capacitor: Registering plugin instance: NativeBiometric
[2025-06-04 15:54:52] 06-04 20:20:35.920  7808  7808 D Capacitor: Registering plugin instance: OtpManager
[2025-06-04 15:54:52] 06-04 20:20:35.923  7808  7808 D Capacitor: Registering plugin instance: UpdateManager
[2025-06-04 15:54:53] 06-04 20:20:35.925  7808  7808 D Capacitor: Registering plugin instance: DigitalWallet
[2025-06-04 15:54:53] 06-04 20:20:35.927  7808  7808 D Capacitor: Registering plugin instance: DigitalWallet
[2025-06-04 15:54:53] 06-04 20:20:35.928  7808  7808 D Capacitor: Registering plugin instance: OnespanBinding
[2025-06-04 15:54:54] 06-04 20:20:35.929  7808  7808 D Capacitor: Registering plugin instance: OneSpanDigipass
[2025-06-04 15:54:54] 06-04 20:20:35.930  7808  7808 D Capacitor: Registering plugin instance: OneSpanSecureStorage
[2025-06-04 15:54:54] 06-04 20:20:35.931  7808  7808 D Capacitor: Registering plugin instance: FileOpener
[2025-06-04 15:54:54] 06-04 20:20:35.934  7808  7808 D Capacitor: Registering plugin instance: App
[2025-06-04 15:54:55] 06-04 20:20:35.938  7808  7808 D Capacitor: Registering plugin instance: Clipboard
[2025-06-04 15:54:55] 06-04 20:20:35.940  7808  7808 D Capacitor: Registering plugin instance: Device
[2025-06-04 15:54:55] 06-04 20:20:35.941  7808  7808 D Capacitor: Registering plugin instance: Filesystem
[2025-06-04 15:54:56] 06-04 20:20:35.943  7808  7808 D Capacitor: Registering plugin instance: LocalNotifications
[2025-06-04 15:54:56] 06-04 20:20:35.949  7808  7808 D Capacitor: Registering plugin instance: Network
[2025-06-04 15:54:56] 06-04 20:20:35.951  7808  7808 D Capacitor: Registering plugin instance: Preferences
[2025-06-04 15:54:56] 06-04 20:20:35.954  7808  7808 D Capacitor: Registering plugin instance: PushNotifications
[2025-06-04 15:54:57] 06-04 20:20:35.958  7808  7808 D Capacitor: Registering plugin instance: Share
[2025-06-04 15:54:57] 06-04 20:20:35.961  7808  7808 D Capacitor: Registering plugin instance: SplashScreen
[2025-06-04 15:54:57] 06-04 20:20:35.966  7808  7808 D Capacitor: Registering plugin instance: StatusBar
[2025-06-04 15:54:57] 06-04 20:20:35.969  7808  7808 D Capacitor: Registering plugin instance: AppReview
[2025-06-04 15:54:58] 06-04 20:20:35.970  7808  7808 D Capacitor: Registering plugin instance: BarcodeScanner
[2025-06-04 15:54:58] 06-04 20:20:35.971  7808  7808 D Capacitor: Registering plugin instance: Contacts
[2025-06-04 15:54:58] 06-04 20:20:35.972  7808  7808 D Capacitor: Registering plugin instance: DeviceManager
[2025-06-04 15:54:59] 06-04 20:20:35.974  7808  7808 D Capacitor: Registering plugin instance: NativeBiometric
[2025-06-04 15:54:59] 06-04 20:20:35.975  7808  7808 D Capacitor: Registering plugin instance: OtpManager
[2025-06-04 15:54:59] 06-04 20:20:35.976  7808  7808 D Capacitor: Registering plugin instance: UpdateManager
[2025-06-04 15:54:59] 06-04 20:20:35.993  7808  7808 W Capacitor: Unable to read file at path public/plugins
[2025-06-04 15:55:00] 06-04 20:20:35.998  7808  7808 D Capacitor: Loading app at http://localhost
[2025-06-04 15:55:00] 06-04 20:20:36.031  7808  7808 D Capacitor/LN: LocalNotification received: null
[2025-06-04 15:55:01] 06-04 20:20:36.031  7808  7808 D Capacitor/LN: Activity started without notification attached
[2025-06-04 15:55:01] 06-04 20:20:36.045  7808  7808 D Capacitor: App started
[2025-06-04 15:55:01] 06-04 20:20:36.049  7808  7808 D Capacitor/AppPlugin: Firing change: true
[2025-06-04 15:55:02] 06-04 20:20:36.054  7808  7808 V Capacitor/AppPlugin: Notifying listeners for event appStateChange
[2025-06-04 15:55:02] 06-04 20:20:36.056  7808  7808 D Capacitor/AppPlugin: No listeners found for event appStateChange
[2025-06-04 15:55:02] 06-04 20:20:36.061  7808  7808 D Capacitor: App resumed
[2025-06-04 15:55:03] 06-04 20:20:36.071  7808  7905 V Capacitor/NetworkPlugin: Notifying listeners for event networkStatusChange
[2025-06-04 15:55:03] 06-04 20:20:36.073  7808  7905 D Capacitor/NetworkPlugin: No listeners found for event networkStatusChange
[2025-06-04 15:55:03] 06-04 20:20:36.129  7808  7857 D Capacitor: Handling local request: http://localhost/
[2025-06-04 15:55:04] 06-04 20:20:36.223  7808  7843 W OpenGLRenderer: Failed to initialize 101010-2 format, error = EGL_SUCCESS
[2025-06-04 15:55:05] 06-04 20:20:36.363  7808  7857 D Capacitor: Handling local request: http://localhost/polyfills.6a7f2f55005cef71.js
[2025-06-04 15:55:05] 06-04 20:20:36.366  7808  7859 D Capacitor: Handling local request: http://localhost/scripts.f17cf755f9c40b06.js
[2025-06-04 15:55:05] 06-04 20:20:36.373  7808  7876 D Capacitor: Handling local request: http://localhost/main.e912543254e913ec.js
[2025-06-04 15:55:05] 06-04 20:20:36.439  7808  7859 D Capacitor: Handling local request: http://localhost/styles.122d27b1ccb0f86e.css
[2025-06-04 15:55:06] 06-04 20:20:36.455  7808  7857 D Capacitor: Handling local request: http://localhost/assets/shared/fonts/poppins/Poppins-Regular.woff2
[2025-06-04 15:55:06] 06-04 20:20:36.785  7808  7857 D Capacitor: Handling local request: http://localhost/7423.ec3d02d0e1698874.js
[2025-06-04 15:55:06] 06-04 20:20:36.794  7808  7857 D Capacitor: Handling local request: http://localhost/6408.d246310352592e73.js
[2025-06-04 15:55:06] 06-04 20:20:36.800  7808  7857 D Capacitor: Handling local request: http://localhost/9418.ac6b1bb6ab9a102a.js
[2025-06-04 15:55:07] 06-04 20:20:36.827  7808  7857 D Capacitor: Handling local request: http://localhost/1412.51269b0452f08973.js
[2025-06-04 15:55:07] 06-04 20:20:36.835  7808  7857 D Capacitor: Handling local request: http://localhost/1179.446455b3c63243bd.js
[2025-06-04 15:55:07] 06-04 20:20:36.840  7808  7857 D Capacitor: Handling local request: http://localhost/9018.9c79b1c253884311.js
[2025-06-04 15:55:08] 06-04 20:20:36.843  7808  7857 D Capacitor: Handling local request: http://localhost/5267.63caea1cddb9837c.js
[2025-06-04 15:55:08] 06-04 20:20:36.844  7808  7859 D Capacitor: Handling local request: http://localhost/7284.72370e4dfe6d427f.js
[2025-06-04 15:55:08] 06-04 20:20:36.850  7808  7859 D Capacitor: Handling local request: http://localhost/7559.bc195ea16f614038.js
[2025-06-04 15:55:09] 06-04 20:20:36.852  7808  7859 D Capacitor: Handling local request: http://localhost/4650.99a220314a61f83a.js
[2025-06-04 15:55:09] 06-04 20:20:36.856  7808  7859 D Capacitor: Handling local request: http://localhost/1181.69ac4c7fb4d79c0d.js
[2025-06-04 15:55:09] 06-04 20:20:36.857  7808  7857 D Capacitor: Handling local request: http://localhost/4080.71f9f6875b165d96.js
[2025-06-04 15:55:09] 06-04 20:20:36.865  7808  7857 D Capacitor: Handling local request: http://localhost/106.f3a245174df5fe78.js
[2025-06-04 15:55:10] 06-04 20:20:36.867  7808  7859 D Capacitor: Handling local request: http://localhost/5548.7c51fdcea7c1a522.js
[2025-06-04 15:55:10] 06-04 20:20:36.869  7808  7876 D Capacitor: Handling local request: http://localhost/9165.d3966ae4f764db64.js
[2025-06-04 15:55:10] 06-04 20:20:36.871  7808  7854 D Capacitor: Handling local request: http://localhost/8255.f66fb77480e7c1e6.js
[2025-06-04 15:55:10] 06-04 20:20:36.877  7808  7854 D Capacitor: Handling local request: http://localhost/2186.530c5e4c24ff8b27.js
[2025-06-04 15:55:11] 06-04 20:20:36.880  7808  7857 D Capacitor: Handling local request: http://localhost/8906.2091cdbf4d7d5437.js
[2025-06-04 15:55:11] 06-04 20:20:36.892  7808  7857 D Capacitor: Handling local request: http://localhost/5107.5fa0c605b71f4b60.js
[2025-06-04 15:55:11] 06-04 20:20:36.894  7808  7854 D Capacitor: Handling local request: http://localhost/364.e263f771a89d7a00.js
[2025-06-04 15:55:12] 06-04 20:20:36.896  7808  7859 D Capacitor: Handling local request: http://localhost/196.7561b2c681ebed1a.js
[2025-06-04 15:55:12] 06-04 20:20:36.904  7808  7857 D Capacitor: Handling local request: http://localhost/7340.2dcccf981059377c.js
[2025-06-04 15:55:12] 06-04 20:20:36.906  7808  7859 D Capacitor: Handling local request: http://localhost/8992.73a58c6738c2fafe.js
[2025-06-04 15:55:13] 06-04 20:20:36.908  7808  7876 D Capacitor: Handling local request: http://localhost/6974.c000a8b9901e56cf.js
[2025-06-04 15:55:13] 06-04 20:20:36.911  7808  7857 D Capacitor: Handling local request: http://localhost/8551.69cffc520417bf55.js
[2025-06-04 15:55:13] 06-04 20:20:36.916  7808  7876 D Capacitor: Handling local request: http://localhost/4793.dc8454974d25a69a.js
[2025-06-04 15:55:13] 06-04 20:20:36.920  7808  7876 D Capacitor: Handling local request: http://localhost/9189.ab78165bc8202dd6.js
[2025-06-04 15:55:14] 06-04 20:20:36.923  7808  7854 D Capacitor: Handling local request: http://localhost/958.c6a652967daab1f8.js
[2025-06-04 15:55:14] 06-04 20:20:36.926  7808  7854 D Capacitor: Handling local request: http://localhost/3469.0d51e4e01ec9b952.js
[2025-06-04 15:55:14] 06-04 20:20:36.927  7808  7876 D Capacitor: Handling local request: http://localhost/314.29be26cc865e86e3.js
[2025-06-04 15:55:15] 06-04 20:20:36.934  7808  7854 D Capacitor: Handling local request: http://localhost/5091.a1569df980795cbf.js
[2025-06-04 15:55:15] 06-04 20:20:36.940  7808  7876 D Capacitor: Handling local request: http://localhost/4858.665dc6a9d6c8febe.js
[2025-06-04 15:55:15] 06-04 20:20:36.943  7808  7876 D Capacitor: Handling local request: http://localhost/529.0c1f62f589ab8557.js
[2025-06-04 15:55:16] 06-04 20:20:36.950  7808  7876 D Capacitor: Handling local request: http://localhost/8650.c8f32dcbd84fcd54.js
[2025-06-04 15:55:16] 06-04 20:20:36.954  7808  7854 D Capacitor: Handling local request: http://localhost/8976.717d08c090457180.js
[2025-06-04 15:55:16] 06-04 20:20:36.955  7808  7876 D Capacitor: Handling local request: http://localhost/9190.f1bc6150ebc00491.js
[2025-06-04 15:55:16] 06-04 20:20:36.958  7808  7876 D Capacitor: Handling local request: http://localhost/5670.151df2dfeffde816.js
[2025-06-04 15:55:17] 06-04 20:20:36.959  7808  7857 D Capacitor: Handling local request: http://localhost/6728.36d39ebe8f114e5d.js
[2025-06-04 15:55:17] 06-04 20:20:36.967  7808  7854 D Capacitor: Handling local request: http://localhost/9184.92b0675a48d27b6b.js
[2025-06-04 15:55:18] 06-04 20:20:36.975  7808  7876 D Capacitor: Handling local request: http://localhost/7321.ac6c6e5fd93f877b.js
[2025-06-04 15:55:18] 06-04 20:20:36.978  7808  7876 D Capacitor: Handling local request: http://localhost/9026.b9ba021a9c7a3ecd.js
[2025-06-04 15:55:18] 06-04 20:20:36.979  7808  7876 D Capacitor: Handling local request: http://localhost/6552.e0ae236273732c36.js
[2025-06-04 15:55:18] 06-04 20:20:36.982  7808  7857 D Capacitor: Handling local request: http://localhost/7206.432f398206b2760b.js
[2025-06-04 15:55:19] 06-04 20:20:36.984  7808  7859 D Capacitor: Handling local request: http://localhost/9389.a55dde133451ce9c.js
[2025-06-04 15:55:19] 06-04 20:20:36.987  7808  7876 D Capacitor: Handling local request: http://localhost/65.f42d234c0d65688a.js
[2025-06-04 15:55:19] 06-04 20:20:36.989  7808  7854 D Capacitor: Handling local request: http://localhost/4572.c087da350593fc3f.js
[2025-06-04 15:55:20] 06-04 20:20:36.990  7808  7857 D Capacitor: Handling local request: http://localhost/9773.3880767336291a26.js
[2025-06-04 15:55:20] 06-04 20:20:36.991  7808  7859 D Capacitor: Handling local request: http://localhost/8532.7607b465408eabc3.js
[2025-06-04 15:55:20] 06-04 20:20:36.992  7808  7858 D Capacitor: Handling local request: http://localhost/9474.d8cbdd0e21a84047.js
[2025-06-04 15:55:21] 06-04 20:20:36.993  7808  7875 D Capacitor: Handling local request: http://localhost/445.4a4f50401e3f8990.js
[2025-06-04 15:55:21] 06-04 20:20:36.997  7808  7858 D Capacitor: Handling local request: http://localhost/4852.58e267ff446bc0db.js
[2025-06-04 15:55:21] 06-04 20:20:36.999  7808  7859 D Capacitor: Handling local request: http://localhost/3353.cc6438b6f1dc6f14.js
[2025-06-04 15:55:21] 06-04 20:20:37.001  7808  7875 D Capacitor: Handling local request: http://localhost/9521.781360d09f249c06.js
[2025-06-04 15:55:22] 06-04 20:20:37.003  7808  7875 D Capacitor: Handling local request: http://localhost/5017.63ec935baaaf32da.js
[2025-06-04 15:55:22] 06-04 20:20:37.004  7808  7876 D Capacitor: Handling local request: http://localhost/9615.718f4dbfaeae5d9f.js
[2025-06-04 15:55:22] 06-04 20:20:37.005  7808  7875 D Capacitor: Handling local request: http://localhost/8205.e5d52a58b606d524.js
[2025-06-04 15:55:22] 06-04 20:20:37.007  7808  7859 D Capacitor: Handling local request: http://localhost/7376.4cb046d59a1922a0.js
[2025-06-04 15:55:23] 06-04 20:20:37.009  7808  7859 D Capacitor: Handling local request: http://localhost/6179.4b7253b766f9cbde.js
[2025-06-04 15:55:23] 06-04 20:20:37.010  7808  7858 D Capacitor: Handling local request: http://localhost/8769.d34677aa9e044f08.js
[2025-06-04 15:55:24] 06-04 20:20:37.013  7808  7858 D Capacitor: Handling local request: http://localhost/2817.43293f8f3f796319.js
[2025-06-04 15:55:24] 06-04 20:20:37.019  7808  7854 D Capacitor: Handling local request: http://localhost/2653.337a4456ae108e2e.js
[2025-06-04 15:55:24] 06-04 20:20:37.020  7808  7876 D Capacitor: Handling local request: http://localhost/2696.81629c75ac5b4beb.js
[2025-06-04 15:55:24] 06-04 20:20:37.022  7808  7859 D Capacitor: Handling local request: http://localhost/1067.7b4cf7d8762ba60b.js
[2025-06-04 15:55:25] 06-04 20:20:37.028  7808  7859 D Capacitor: Handling local request: http://localhost/5838.2f12306386abc110.js
[2025-06-04 15:55:25] 06-04 20:20:37.029  7808  7875 D Capacitor: Handling local request: http://localhost/3850.2ea594ed81c9a2a5.js
[2025-06-04 15:55:26] 06-04 20:20:37.962  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785120, pluginId: App, methodName: getLaunchUrl
[2025-06-04 15:55:26] 06-04 20:20:37.962  7808  7808 V Capacitor: callback: 785120, pluginId: App, methodName: getLaunchUrl, methodData: {}
[2025-06-04 15:55:26] 06-04 20:20:37.964  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785121, pluginId: Preferences, methodName: get
[2025-06-04 15:55:26] 06-04 20:20:37.966  7808  7808 V Capacitor: callback: 785121, pluginId: Preferences, methodName: get, methodData: {"key":"customer"}
[2025-06-04 15:55:27] 06-04 20:20:37.971  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785122, pluginId: App, methodName: addListener
[2025-06-04 15:55:27] 06-04 20:20:37.971  7808  7808 V Capacitor: callback: 785122, pluginId: App, methodName: addListener, methodData: {"eventName":"backButton"}
[2025-06-04 15:55:27] 06-04 20:20:37.988  7808  7875 D Capacitor: Handling local request: http://localhost/9652.d59a3e9f7826c2d7.js
[2025-06-04 15:55:28] 06-04 20:20:37.989  7808  7854 D Capacitor: Handling local request: http://localhost/2263.eb7346938a015273.js
[2025-06-04 15:55:28] 06-04 20:20:37.995  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785123, pluginId: Network, methodName: getStatus
[2025-06-04 15:55:28] 06-04 20:20:37.995  7808  7808 V Capacitor: callback: 785123, pluginId: Network, methodName: getStatus, methodData: {}
[2025-06-04 15:55:29] 06-04 20:20:37.995  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785124, pluginId: Network, methodName: addListener
[2025-06-04 15:55:29] 06-04 20:20:37.996  7808  7808 V Capacitor: callback: 785124, pluginId: Network, methodName: addListener, methodData: {"eventName":"networkStatusChange"}
[2025-06-04 15:55:29] 06-04 20:20:37.999  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785125, pluginId: SplashScreen, methodName: hide
[2025-06-04 15:55:30] 06-04 20:20:37.999  7808  7808 V Capacitor: callback: 785125, pluginId: SplashScreen, methodName: hide, methodData: {}
[2025-06-04 15:55:30] 06-04 20:20:38.004  7808  7875 D Capacitor: Handling local request: http://localhost/assets/shared/logos/modals/app-review.svg
[2025-06-04 15:55:30] 06-04 20:20:38.015  7808  7875 D Capacitor: Handling local request: http://localhost/assets/shared/animations/blue-occidente-spinner.json
[2025-06-04 15:55:31] 06-04 20:20:38.032  7808  7854 D Capacitor: Handling local request: http://localhost/bocc-mb-icons.13242fd4d6d671b8.ttf?ukugsn
[2025-06-04 15:55:31] 06-04 20:20:38.038  7808  7876 D Capacitor: Handling local request: http://localhost/assets/shared/fonts/poppins/Poppins-Medium.woff2
[2025-06-04 15:55:31] 06-04 20:20:38.039  7808  7876 D Capacitor: Handling local request: http://localhost/assets/shared/fonts/poppins/Poppins-SemiBold.woff2
[2025-06-04 15:55:31] 06-04 20:20:38.068  7808  7808 I Capacitor/Console: File:  - Line 353 - Msg: undefined
[2025-06-04 15:55:32] 06-04 20:20:38.071  7808  7808 E Capacitor/Console: File: http://localhost/4650.99a220314a61f83a.js - Line 1 - Msg: ERROR Error: Uncaught (in promise): TypeError: Cannot read properties of undefined (reading 'url')
[2025-06-04 15:55:32] 06-04 20:20:38.071  7808  7808 E Capacitor/Console: TypeError: Cannot read properties of undefined (reading 'url')
[2025-06-04 15:55:32] 06-04 20:20:38.071  7808  7808 E Capacitor/Console:     at http://localhost/3850.2ea594ed81c9a2a5.js:18:353222
[2025-06-04 15:55:33] 06-04 20:20:38.071  7808  7808 E Capacitor/Console:     at b.invoke (http://localhost/polyfills.6a7f2f55005cef71.js:1:7729)
[2025-06-04 15:55:33] 06-04 20:20:38.071  7808  7808 E Capacitor/Console:     at Object.onInvoke (http://localhost/4650.99a220314a61f83a.js:1:155527)
[2025-06-04 15:55:33] 06-04 20:20:38.071  7808  7808 E Capacitor/Console:     at b.invoke (http://localhost/polyfills.6a7f2f55005cef71.js:1:7668)
[2025-06-04 15:55:33] 06-04 20:20:38.071  7808  7808 E Capacitor/Console:     at b.run (http://localhost/polyfills.6a7f2f55005cef71.js:1:2728)
[2025-06-04 15:55:34] 06-04 20:20:38.071  7808  7808 E Capacitor/Console:     at http://localhost/polyfills.6a7f2f55005cef71.js:1:18696
[2025-06-04 15:55:34] 06-04 20:20:38.071  7808  7808 E Capacitor/Console:     at b.invokeTask (http://localhost/polyfills.6a7f2f55005cef71.js:1:8414)
[2025-06-04 15:55:34] 06-04 20:20:38.071  7808  7808 E Capacitor/Console:     at Object.onInvokeTask (http://localhost/4650.99a220314a61f83a.js:1:155343)
[2025-06-04 15:55:35] 06-04 20:20:38.071  7808  7808 E Capacitor/Console:     at b.invokeTask (http://localhost/polyfills.6a7f2f55005cef71.js:1:8335)
[2025-06-04 15:55:35] 06-04 20:20:38.071  7808  7808 E Capacitor/Console:     at b.runTask (http://localhost/polyfills.6a7f2f55005cef71.js:1:3409)
[2025-06-04 15:55:35] 06-04 20:20:38.072  7808  7808 I Capacitor/Console: File:  - Line 353 - Msg: undefined
[2025-06-04 15:55:36] 06-04 20:20:38.075  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785126, pluginId: Preferences, methodName: get
[2025-06-04 15:55:36] 06-04 20:20:38.077  7808  7808 V Capacitor: callback: 785126, pluginId: Preferences, methodName: get, methodData: {"key":"migration"}
[2025-06-04 15:55:36] 06-04 20:20:38.080  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785127, pluginId: Preferences, methodName: get
[2025-06-04 15:55:37] 06-04 20:20:38.081  7808  7808 V Capacitor: callback: 785127, pluginId: Preferences, methodName: get, methodData: {"key":"migration_v5"}
[2025-06-04 15:55:37] 06-04 20:20:38.238  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785128, pluginId: DeviceManager, methodName: hasHuaweiServices
[2025-06-04 15:55:37] 06-04 20:20:38.239  7808  7808 V Capacitor: callback: 785128, pluginId: DeviceManager, methodName: hasHuaweiServices, methodData: {}
[2025-06-04 15:55:38] 06-04 20:20:38.295  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785129, pluginId: PushNotifications, methodName: checkPermissions
[2025-06-04 15:55:38] 06-04 20:20:38.295  7808  7808 V Capacitor: callback: 785129, pluginId: PushNotifications, methodName: checkPermissions, methodData: {}
[2025-06-04 15:55:38] 06-04 20:20:38.333  7808  7858 D Capacitor: Handling local request: http://localhost/4039.771677ef50916e9d.js
[2025-06-04 15:55:39] 06-04 20:20:38.365  7808  7876 D Capacitor: Handling local request: http://localhost/8171.4f577a4bd91d9dd5.js
[2025-06-04 15:55:39] 06-04 20:20:38.373  7808  7876 D Capacitor: Handling local request: http://localhost/9822.f2e86a6b7ea2d938.js
[2025-06-04 15:55:39] 06-04 20:20:38.443  7808  7854 D Capacitor: Handling local request: http://localhost/8332.0cdbbb4b561eaa7a.js
[2025-06-04 15:55:40] 06-04 20:20:38.448  7808  7858 D Capacitor: Handling local request: http://localhost/1816.685fe25ad78a4abf.js
[2025-06-04 15:55:40] 06-04 20:20:38.451  7808  7858 D Capacitor: Handling local request: http://localhost/3947.c6b9d680441dbd7e.js
[2025-06-04 15:55:40] 06-04 20:20:38.461  7808  7858 D Capacitor: Handling local request: http://localhost/111.80ce1d5d8428bdfe.js
[2025-06-04 15:55:40] 06-04 20:20:38.464  7808  7858 D Capacitor: Handling local request: http://localhost/300.458870cc07ab0380.js
[2025-06-04 15:55:41] 06-04 20:20:38.464  7808  7854 D Capacitor: Handling local request: http://localhost/8285.7ded78778f756258.js
[2025-06-04 15:55:41] 06-04 20:20:38.464  7808  7876 D Capacitor: Handling local request: http://localhost/1914.de1f946e15bd8064.js
[2025-06-04 15:55:41] 06-04 20:20:38.467  7808  7876 D Capacitor: Handling local request: http://localhost/2243.15300921c2e7ed36.js
[2025-06-04 15:55:42] 06-04 20:20:38.468  7808  7876 D Capacitor: Handling local request: http://localhost/7800.0459fa99e6d1382a.js
[2025-06-04 15:55:42] 06-04 20:20:38.470  7808  7854 D Capacitor: Handling local request: http://localhost/4035.850a60a55fd278a5.js
[2025-06-04 15:55:42] 06-04 20:20:38.471  7808  7858 D Capacitor: Handling local request: http://localhost/8239.31d2e21c5b55fdca.js
[2025-06-04 15:55:42] 06-04 20:20:38.473  7808  7876 D Capacitor: Handling local request: http://localhost/2824.554dd4ab375ace54.js
[2025-06-04 15:55:43] 06-04 20:20:38.498  7808  7854 D Capacitor: Handling local request: http://localhost/assets/shared/logos/aval-white.svg
[2025-06-04 15:55:43] 06-04 20:20:38.505  7808  7876 D Capacitor: Handling local request: http://localhost/assets/shared/logos/banco-occidente-vertical-white.svg
[2025-06-04 15:55:43] 06-04 20:20:38.593  7808  7808 I Capacitor/Console: File: http://localhost/2653.337a4456ae108e2e.js - Line 1 - Msg: ConfigCat - INFO - [5000] Evaluating 'mathilde'
[2025-06-04 15:55:44] 06-04 20:20:38.593  7808  7808 I Capacitor/Console:   Returning 'true'.
[2025-06-04 15:55:44] 06-04 20:20:38.618  7808  7858 D Capacitor: Handling local request: http://localhost/7791.4da3863b83ae3982.js
[2025-06-04 15:55:44] 06-04 20:20:38.619  7808  7858 D Capacitor: Handling local request: http://localhost/7648.4564fb90d7b86c8f.js
[2025-06-04 15:55:45] 06-04 20:20:38.641  7808  7859 D Capacitor: Handling local request: http://localhost/8848.fb4893a20f068696.js
[2025-06-04 15:55:45] 06-04 20:20:38.643  7808  7875 D Capacitor: Handling local request: http://localhost/6631.d9b202387bf549cb.js
[2025-06-04 15:55:45] 06-04 20:20:38.646  7808  7857 D Capacitor: Handling local request: http://localhost/3035.65064b54f46df67b.js
[2025-06-04 15:55:45] 06-04 20:20:38.646  7808  7858 D Capacitor: Handling local request: http://localhost/8722.512e2d98583390fd.js
[2025-06-04 15:55:46] 06-04 20:20:38.647  7808  7859 D Capacitor: Handling local request: http://localhost/525.a336db17b48c458d.js
[2025-06-04 15:55:46] 06-04 20:20:38.649  7808  7876 D Capacitor: Handling local request: http://localhost/1051.2440dfef4c64bf4e.js
[2025-06-04 15:55:46] 06-04 20:20:38.668  7808  7859 D Capacitor: Handling local request: http://localhost/4810.d9466eeff7a599ce.js
[2025-06-04 15:55:47] 06-04 20:20:38.672  7808  7876 D Capacitor: Handling local request: http://localhost/6273.2abaf3daf31cbaa4.js
[2025-06-04 15:55:47] 06-04 20:20:38.681  7808  7876 D Capacitor: Handling local request: http://localhost/1927.7d4180ddecf441be.js
[2025-06-04 15:55:47] 06-04 20:20:38.684  7808  7854 D Capacitor: Handling local request: http://localhost/3271.8db26b4f7054f805.js
[2025-06-04 15:55:48] 06-04 20:20:38.691  7808  7859 D Capacitor: Handling local request: http://localhost/1562.6728adecb9a4b6d8.js
[2025-06-04 15:55:48] 06-04 20:20:38.691  7808  7875 D Capacitor: Handling local request: http://localhost/9541.14d2c30390e49752.js
[2025-06-04 15:55:48] 06-04 20:20:38.697  7808  7859 D Capacitor: Handling local request: http://localhost/3923.ceb65a33a09e95da.js
[2025-06-04 15:55:49] 06-04 20:20:38.701  7808  7875 D Capacitor: Handling local request: http://localhost/5412.881ae8f20f9c6955.js
[2025-06-04 15:55:49] 06-04 20:20:38.702  7808  7859 D Capacitor: Handling local request: http://localhost/2788.21992c121f016963.js
[2025-06-04 15:55:49] 06-04 20:20:38.708  7808  7854 D Capacitor: Handling local request: http://localhost/5833.e0c39a3889d69a92.js
[2025-06-04 15:55:49] 06-04 20:20:38.708  7808  7876 D Capacitor: Handling local request: http://localhost/283.57861ffed8675486.js
[2025-06-04 15:55:50] 06-04 20:20:38.709  7808  7859 D Capacitor: Handling local request: http://localhost/4985.1f724a51d83175ef.js
[2025-06-04 15:55:50] 06-04 20:20:38.715  7808  7876 D Capacitor: Handling local request: http://localhost/8865.415ea7aaada71fe0.js
[2025-06-04 15:55:50] 06-04 20:20:38.717  7808  7876 D Capacitor: Handling local request: http://localhost/7777.d50c5c20ccef987b.js
[2025-06-04 15:55:51] 06-04 20:20:38.719  7808  7876 D Capacitor: Handling local request: http://localhost/3139.804ea798f7e53f9b.js
[2025-06-04 15:55:51] 06-04 20:20:38.723  7808  7876 D Capacitor: Handling local request: http://localhost/6230.445be33e047d4afd.js
[2025-06-04 15:55:51] 06-04 20:20:38.762  7808  7859 D Capacitor: Handling local request: http://localhost/2966.612d898e1c2e5725.js
[2025-06-04 15:55:51] 06-04 20:20:38.768  7808  7854 D Capacitor: Handling local request: http://localhost/1982.f05adc1bee652c68.js
[2025-06-04 15:55:52] 06-04 20:20:38.772  7808  7859 D Capacitor: Handling local request: http://localhost/9698.be1ef0b301046322.js
[2025-06-04 15:55:52] 06-04 20:20:38.773  7808  7859 D Capacitor: Handling local request: http://localhost/8181.ac96d3a8c61d620f.js
[2025-06-04 15:55:52] 06-04 20:20:38.779  7808  7876 D Capacitor: Handling local request: http://localhost/6550.2fa40d1055e81978.js
[2025-06-04 15:55:53] 06-04 20:20:38.782  7808  7875 D Capacitor: Handling local request: http://localhost/9406.47d95dfcf2114ff6.js
[2025-06-04 15:55:53] 06-04 20:20:39.105  7808  7876 D Capacitor: Handling local request: http://localhost/6338.d2f4959e47196d80.js
[2025-06-04 15:55:53] 06-04 20:20:39.108  7808  7876 D Capacitor: Handling local request: http://localhost/6612.6decdc4481a078bd.js
[2025-06-04 15:55:54] 06-04 20:20:39.110  7808  7875 D Capacitor: Handling local request: http://localhost/7177.e337bc36757e963b.js
[2025-06-04 15:55:54] 06-04 20:20:39.116  7808  7875 D Capacitor: Handling local request: http://localhost/8267.706caecb6bc745dc.js
[2025-06-04 15:55:54] 06-04 20:20:39.120  7808  7858 D Capacitor: Handling local request: http://localhost/2687.9e9a9c2c287c8e46.js
[2025-06-04 15:55:54] 06-04 20:20:39.121  7808  7859 D Capacitor: Handling local request: http://localhost/5334.4bca86d528853145.js
[2025-06-04 15:55:55] 06-04 20:20:39.121  7808  7854 D Capacitor: Handling local request: http://localhost/2879.c6bcb0e646113451.js
[2025-06-04 15:55:55] 06-04 20:20:39.123  7808  7858 D Capacitor: Handling local request: http://localhost/8240.bc55bccc7c085ddb.js
[2025-06-04 15:55:55] 06-04 20:20:39.150  7808  7858 D Capacitor: Handling local request: http://localhost/8705.2373f56cb544920a.js
[2025-06-04 15:55:56] 06-04 20:20:39.152  7808  7858 D Capacitor: Handling local request: http://localhost/7420.acf67df91ff0f397.js
[2025-06-04 15:55:56] 06-04 20:20:39.154  7808  7858 D Capacitor: Handling local request: http://localhost/2021.3cb57dfc6db9c60f.js
[2025-06-04 15:55:56] 06-04 20:20:39.156  7808  7859 D Capacitor: Handling local request: http://localhost/3245.ad8fec631ea8b06b.js
[2025-06-04 15:55:56] 06-04 20:20:39.158  7808  7854 D Capacitor: Handling local request: http://localhost/6496.3a446b87a246f81f.js
[2025-06-04 15:55:57] 06-04 20:20:39.158  7808  7875 D Capacitor: Handling local request: http://localhost/7356.c090fbdb13e72ce5.js
[2025-06-04 15:55:57] 06-04 20:20:39.160  7808  7857 D Capacitor: Handling local request: http://localhost/1638.0cfc8bdf99cfe7f4.js
[2025-06-04 15:55:57] 06-04 20:20:39.182  7808  7857 D Capacitor: Handling local request: http://localhost/7095.00eeb82d18aab97e.js
[2025-06-04 15:55:58] 06-04 20:20:39.194  7808  7857 D Capacitor: Handling local request: http://localhost/9407.e4266036fc5a3435.js
[2025-06-04 15:55:58] 06-04 20:20:39.195  7808  7858 D Capacitor: Handling local request: http://localhost/1639.edb8bc5f77af80d9.js
[2025-06-04 15:55:58] 06-04 20:20:39.197  7808  7857 D Capacitor: Handling local request: http://localhost/5062.e3bacf84e978995b.js
[2025-06-04 15:55:58] 06-04 20:20:39.208  7808  7854 D Capacitor: Handling local request: http://localhost/3116.30fdb5bcd60b69ca.js
[2025-06-04 15:55:59] 06-04 20:20:39.209  7808  7875 D Capacitor: Handling local request: http://localhost/6879.1ebce0d2445bd71d.js
[2025-06-04 15:55:59] 06-04 20:20:39.220  7808  7859 D Capacitor: Handling local request: http://localhost/9961.ff957516f747c657.js
[2025-06-04 15:55:59] 06-04 20:20:39.221  7808  7859 D Capacitor: Handling local request: http://localhost/4210.d4cbf9b6b824c15d.js
[2025-06-04 15:56:00] 06-04 20:20:39.230  7808  7854 D Capacitor: Handling local request: http://localhost/1969.b5e090c5392c4d72.js
[2025-06-04 15:56:00] 06-04 20:20:39.233  7808  7875 D Capacitor: Handling local request: http://localhost/9846.d63b22c226c06f98.js
[2025-06-04 15:56:00] 06-04 20:20:39.238  7808  7875 D Capacitor: Handling local request: http://localhost/3063.e65ef51073c76317.js
[2025-06-04 15:56:00] 06-04 20:20:39.346  7808  7859 D Capacitor: Handling local request: http://localhost/571.20b8665731d16cbd.js
[2025-06-04 15:56:01] 06-04 20:20:39.369  7808  7859 D Capacitor: Handling local request: http://localhost/616.3c91004c0bfce945.js
[2025-06-04 15:56:01] 06-04 20:20:39.372  7808  7859 D Capacitor: Handling local request: http://localhost/351.cc8ef1649a7df0e9.js
[2025-06-04 15:56:01] 06-04 20:20:39.378  7808  7859 D Capacitor: Handling local request: http://localhost/788.b443cac40ec5f67c.js
[2025-06-04 15:56:02] 06-04 20:20:39.382  7808  7857 D Capacitor: Handling local request: http://localhost/344.f204c633e78ac698.js
[2025-06-04 15:56:02] 06-04 20:20:39.384  7808  7857 D Capacitor: Handling local request: http://localhost/158.3571d8eac4dcbead.js
[2025-06-04 15:56:02] 06-04 20:20:39.394  7808  7857 D Capacitor: Handling local request: http://localhost/7762.7d7316ce514144d8.js
[2025-06-04 15:56:03] 06-04 20:20:39.397  7808  7859 D Capacitor: Handling local request: http://localhost/3882.17cd7c186ea61d70.js
[2025-06-04 15:56:03] 06-04 20:20:39.398  7808  7859 D Capacitor: Handling local request: http://localhost/7581.97ff65d05cd01a7d.js
[2025-06-04 15:56:03] 06-04 20:20:39.406  7808  7859 D Capacitor: Handling local request: http://localhost/2354.10cbd5e95350a3ca.js
[2025-06-04 15:56:03] 06-04 20:20:39.408  7808  7859 D Capacitor: Handling local request: http://localhost/5156.efb3e741e65c129a.js
[2025-06-04 15:56:04] 06-04 20:20:39.420  7808  7875 D Capacitor: Handling local request: http://localhost/6767.554feba9c22d810b.js
[2025-06-04 15:56:04] 06-04 20:20:39.426  7808  7859 D Capacitor: Handling local request: http://localhost/1951.3ecbfcc2e27aedd8.js
[2025-06-04 15:56:04] 06-04 20:20:39.443  7808  7875 D Capacitor: Handling local request: http://localhost/2880.a6fb9b3e358a9f42.js
[2025-06-04 15:56:05] 06-04 20:20:39.455  7808  7875 D Capacitor: Handling local request: http://localhost/861.61e702c061ddbebb.js
[2025-06-04 15:56:05] 06-04 20:20:39.458  7808  7875 D Capacitor: Handling local request: http://localhost/9041.acbbcd799492e368.js
[2025-06-04 15:56:05] 06-04 20:20:39.458  7808  7875 D Capacitor: Handling local request: http://localhost/3142.c63a40939453016d.js
[2025-06-04 15:56:05] 06-04 20:20:39.459  7808  7875 D Capacitor: Handling local request: http://localhost/7776.a653b0b8443c32fc.js
[2025-06-04 15:56:06] 06-04 20:20:39.461  7808  7859 D Capacitor: Handling local request: http://localhost/1618.8d3268304155c065.js
[2025-06-04 15:56:06] 06-04 20:20:39.465  7808  7857 D Capacitor: Handling local request: http://localhost/3692.2a711cd4c387393d.js
[2025-06-04 15:56:06] 06-04 20:20:39.465  7808  7857 D Capacitor: Handling local request: http://localhost/9319.edf532b7ecf38088.js
[2025-06-04 15:56:07] 06-04 20:20:39.467  7808  7859 D Capacitor: Handling local request: http://localhost/4813.7f35040def932616.js
[2025-06-04 15:56:07] 06-04 20:20:39.481  7808  7857 D Capacitor: Handling local request: http://localhost/8877.1f0691c0b1f07841.js
[2025-06-04 15:56:07] 06-04 20:20:39.482  7808  7875 D Capacitor: Handling local request: http://localhost/3112.e6d697e4bd4a3613.js
[2025-06-04 15:56:07] 06-04 20:20:39.486  7808  7875 D Capacitor: Handling local request: http://localhost/6387.33b692161579ccbf.js
[2025-06-04 15:56:08] 06-04 20:20:39.493  7808  7875 D Capacitor: Handling local request: http://localhost/2974.245920e6dab2fee3.js
[2025-06-04 15:56:08] 06-04 20:20:39.494  7808  7859 D Capacitor: Handling local request: http://localhost/1023.b2b633df8eaad8bd.js
[2025-06-04 15:56:08] 06-04 20:20:39.495  7808  7857 D Capacitor: Handling local request: http://localhost/2654.2053e5444d1cef1e.js
[2025-06-04 15:56:09] 06-04 20:20:39.496  7808  7857 D Capacitor: Handling local request: http://localhost/2024.7826b6f1c88f0ec9.js
[2025-06-04 15:56:09] 06-04 20:20:39.497  7808  7858 D Capacitor: Handling local request: http://localhost/8538.4108669802a4f439.js
[2025-06-04 15:56:09] 06-04 20:20:39.503  7808  7858 D Capacitor: Handling local request: http://localhost/6782.e1e0e6ee948547ea.js
[2025-06-04 15:56:10] 06-04 20:20:39.512  7808  7858 D Capacitor: Handling local request: http://localhost/7075.01d29264bcf563b1.js
[2025-06-04 15:56:10] 06-04 20:20:39.522  7808  7854 D Capacitor: Handling local request: http://localhost/4707.9b38ffbe00dd8c3e.js
[2025-06-04 15:56:10] 06-04 20:20:39.531  7808  7854 D Capacitor: Handling local request: http://localhost/5650.5f9329712bb8433c.js
[2025-06-04 15:56:10] 06-04 20:20:39.533  7808  7858 D Capacitor: Handling local request: http://localhost/7479.9a3a20d9cb4dfd07.js
[2025-06-04 15:56:11] 06-04 20:20:39.534  7808  7854 D Capacitor: Handling local request: http://localhost/1430.16ebd91105c4c56a.js
[2025-06-04 15:56:11] 06-04 20:20:39.543  7808  7875 D Capacitor: Handling local request: http://localhost/9557.49cbd1b65f286642.js
[2025-06-04 15:56:11] 06-04 20:20:39.554  7808  7875 D Capacitor: Handling local request: http://localhost/2479.15854f8d8424678a.js
[2025-06-04 15:56:12] 06-04 20:20:39.555  7808  7854 D Capacitor: Handling local request: http://localhost/8755.3118545c4db19684.js
[2025-06-04 15:56:12] 06-04 20:20:39.558  7808  7854 D Capacitor: Handling local request: http://localhost/3501.e1eb684d26279193.js
[2025-06-04 15:56:12] 06-04 20:20:39.560  7808  7854 D Capacitor: Handling local request: http://localhost/9696.e55fd12b172a8b25.js
[2025-06-04 15:56:12] 06-04 20:20:39.568  7808  7854 D Capacitor: Handling local request: http://localhost/3242.09a91344ce675f17.js
[2025-06-04 15:56:13] 06-04 20:20:39.570  7808  7857 D Capacitor: Handling local request: http://localhost/6797.c918d6c32fedf59d.js
[2025-06-04 15:56:13] 06-04 20:20:39.572  7808  7875 D Capacitor: Handling local request: http://localhost/4006.b4b2d16c59bff2e5.js
[2025-06-04 15:56:13] 06-04 20:20:39.577  7808  7858 D Capacitor: Handling local request: http://localhost/9221.9d769087fcda9806.js
[2025-06-04 15:56:14] 06-04 20:20:39.578  7808  7858 D Capacitor: Handling local request: http://localhost/6406.a363efdbedbbd3a1.js
[2025-06-04 15:56:14] 06-04 20:20:39.578  7808  7858 D Capacitor: Handling local request: http://localhost/5842.3203fa762bcac2ba.js
[2025-06-04 15:56:14] 06-04 20:20:39.579  7808  7859 D Capacitor: Handling local request: http://localhost/7600.78f9653c82ce629c.js
[2025-06-04 15:56:14] 06-04 20:20:39.579  7808  7858 D Capacitor: Handling local request: http://localhost/991.dcf576ce0c979157.js
[2025-06-04 15:56:15] 06-04 20:20:39.579  7808  7859 D Capacitor: Handling local request: http://localhost/6837.04b57e228e6707b8.js
[2025-06-04 15:56:15] 06-04 20:20:39.587  7808  7875 D Capacitor: Handling local request: http://localhost/9077.952e815d33266f4e.js
[2025-06-04 15:56:15] 06-04 20:20:39.667  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 83 - Msg: UTAG Extension Loaded...
[2025-06-04 15:56:16] 06-04 20:20:39.672  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 108 - Msg: delete cookies document.location.pathname :>>  /authentication/manager-update
[2025-06-04 15:56:16] 06-04 20:20:39.673  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 111 - Msg: onUrlChange product_0CC or SecFormDataCard_ADL deleted
[2025-06-04 15:56:16] 06-04 20:20:39.674  7808  7808 E Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 123 - Msg: 🚀 MTH: Audience API (Cognito)
[2025-06-04 15:56:17] 06-04 20:20:39.685  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4178 - Msg: 🚀 MTH: Pauta getDeviceType
[2025-06-04 15:56:17] 06-04 20:20:39.685  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4188 - Msg: 🚀 MTH: Pauta mathildeSpaces
[2025-06-04 15:56:17] 06-04 20:20:39.685  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4188 - Msg: 🚀 MTH: Pauta mthCreateUrl
[2025-06-04 15:56:18] 06-04 20:20:39.686  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4205 - Msg: 🚀 MTH: Pauta mathildeFetchData
[2025-06-04 15:56:18] 06-04 20:20:39.687  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4212 - Msg: 🚀 MTH: Pauta spacesList
[2025-06-04 15:56:18] 06-04 20:20:39.688  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4212 - Msg: 🚀 MTH: Pauta flickerlesslyConfigList
[2025-06-04 15:56:18] 06-04 20:20:39.689  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4212 - Msg: 🚀 MTH: Pauta mth-flickerlesslyConfigList  [object Object]
[2025-06-04 15:56:19] 06-04 20:20:39.689  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4212 - Msg: 🚀 MTH: Pauta Flickerlessly  [object Object]
[2025-06-04 15:56:19] 06-04 20:20:39.691  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4212 - Msg: 🚀 MTH: Pauta mthRedirectTagAvalCustomize
[2025-06-04 15:56:19] 06-04 20:20:39.714  7808  7859 D Capacitor: Handling local request: http://localhost/8058.1ce16d35063f7b69.js
[2025-06-04 15:56:20] 06-04 20:20:39.723  7808  7854 D Capacitor: Handling local request: http://localhost/9641.de00b93da1696690.js
[2025-06-04 15:56:20] 06-04 20:20:39.725  7808  7859 D Capacitor: Handling local request: http://localhost/7533.b197e59db264ab90.js
[2025-06-04 15:56:20] 06-04 20:20:39.726  7808  7854 D Capacitor: Handling local request: http://localhost/4753.829c297e36b26403.js
[2025-06-04 15:56:21] 06-04 20:20:39.738  7808  7875 D Capacitor: Handling local request: http://localhost/4386.69bf7212633a4d90.js
[2025-06-04 15:56:21] 06-04 20:20:39.743  7808  7875 D Capacitor: Handling local request: http://localhost/2836.cdb9c49b09257845.js
[2025-06-04 15:56:21] 06-04 20:20:39.747  7808  7875 D Capacitor: Handling local request: http://localhost/254.327a0a2fd7452c6b.js
[2025-06-04 15:56:21] 06-04 20:20:39.748  7808  7875 D Capacitor: Handling local request: http://localhost/8477.509acba2e10e18de.js
[2025-06-04 15:56:22] 06-04 20:20:39.755  7808  7854 D Capacitor: Handling local request: http://localhost/6648.c625e6a32befd6b8.js
[2025-06-04 15:56:22] 06-04 20:20:39.755  7808  7875 D Capacitor: Handling local request: http://localhost/260.bcc85e7fac548d9a.js
[2025-06-04 15:56:22] 06-04 20:20:39.766  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.sync.js - Line 7 - Msg: ---->>>>>> adobe_target_visitor_api: OK
[2025-06-04 15:56:23] 06-04 20:20:39.994  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.sync.js - Line 921 - Msg: ---->>>>>> Adobe Target 2.11.4.JS: Lodaded...
[2025-06-04 15:56:23] 06-04 20:20:40.142  7808  7808 W Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.sync.js - Line 928 - Msg: Failed to execute 'write' on 'Document': It isn't possible to write into a document from an asynchronously-loaded external script unless it is explicitly opened.
[2025-06-04 15:56:23] 06-04 20:20:40.145  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.sync.js - Line 294 - Msg: at_property
[2025-06-04 15:56:24] 06-04 20:20:40.213  7808  7854 D Capacitor: Handling local request: http://localhost/6962.e814fe2a8142577c.js
[2025-06-04 15:56:24] 06-04 20:20:40.216  7808  7854 D Capacitor: Handling local request: http://localhost/3168.aecef3ef7f6ff340.js
[2025-06-04 15:56:24] 06-04 20:20:40.219  7808  7854 D Capacitor: Handling local request: http://localhost/1992.6cf5b037b6960ca0.js
[2025-06-04 15:56:25] 06-04 20:20:40.233  7808  7876 D Capacitor: Handling local request: http://localhost/6292.c29a1c9a6efba3e1.js
[2025-06-04 15:56:25] 06-04 20:20:40.252  7808  7876 D Capacitor: Handling local request: http://localhost/4554.c4797cc53d4a2d27.js
[2025-06-04 15:56:25] 06-04 20:20:40.257  7808  7876 D Capacitor: Handling local request: http://localhost/9566.2d313dd102ef6f9a.js
[2025-06-04 15:56:25] 06-04 20:20:40.280  7808  7875 D Capacitor: Handling local request: http://localhost/2799.9255a8162e0cf796.js
[2025-06-04 15:56:26] 06-04 20:20:40.284  7808  7875 D Capacitor: Handling local request: http://localhost/1765.c88407711bda7204.js
[2025-06-04 15:56:26] 06-04 20:20:40.289  7808  7876 D Capacitor: Handling local request: http://localhost/6798.1770b47663e7b366.js
[2025-06-04 15:56:26] 06-04 20:20:40.291  7808  7875 D Capacitor: Handling local request: http://localhost/5597.7cb1756d5c5f1e66.js
[2025-06-04 15:56:27] 06-04 20:20:40.293  7808  7876 D Capacitor: Handling local request: http://localhost/4432.b34034de9efc5720.js
[2025-06-04 15:56:27] 06-04 20:20:40.296  7808  7876 D Capacitor: Handling local request: http://localhost/4208.20b0197ff634e6c2.js
[2025-06-04 15:56:27] 06-04 20:20:40.299  7808  7854 D Capacitor: Handling local request: http://localhost/6263.4a96328145d99d72.js
[2025-06-04 15:56:27] 06-04 20:20:40.304  7808  7854 D Capacitor: Handling local request: http://localhost/2107.fda3d769d4cb467e.js
[2025-06-04 15:56:28] 06-04 20:20:40.313  7808  7876 D Capacitor: Handling local request: http://localhost/9838.08018f054d694626.js
[2025-06-04 15:56:28] 06-04 20:20:40.314  7808  7854 D Capacitor: Handling local request: http://localhost/4087.feb3d2d0006b19fa.js
[2025-06-04 15:56:28] 06-04 20:20:40.315  7808  7876 D Capacitor: Handling local request: http://localhost/496.c0f6afdbb21bf7d7.js
[2025-06-04 15:56:29] 06-04 20:20:40.320  7808  7854 D Capacitor: Handling local request: http://localhost/8374.9c8215b51fb290a2.js
[2025-06-04 15:56:29] 06-04 20:20:40.323  7808  7854 D Capacitor: Handling local request: http://localhost/6123.47ff67dd0bb922f5.js
[2025-06-04 15:56:29] 06-04 20:20:40.330  7808  7876 D Capacitor: Handling local request: http://localhost/3580.1fc9c32aa17b70c0.js
[2025-06-04 15:56:29] 06-04 20:20:40.330  7808  7854 D Capacitor: Handling local request: http://localhost/4092.8749f1a83030e5f5.js
[2025-06-04 15:56:30] 06-04 20:20:40.337  7808  7854 D Capacitor: Handling local request: http://localhost/1455.660a42d1ebba9fed.js
[2025-06-04 15:56:30] 06-04 20:20:40.339  7808  7857 D Capacitor: Handling local request: http://localhost/5138.1c3cc9310308fa23.js
[2025-06-04 15:56:30] 06-04 20:20:40.342  7808  7876 D Capacitor: Handling local request: http://localhost/3284.7be6eb8440e4d10f.js
[2025-06-04 15:56:31] 06-04 20:20:40.344  7808  7875 D Capacitor: Handling local request: http://localhost/2236.c707045148f1293b.js
[2025-06-04 15:56:31] 06-04 20:20:40.371  7808  7876 D Capacitor: Handling local request: http://localhost/5282.f91f5c21a542f927.js
[2025-06-04 15:56:31] 06-04 20:20:40.383  7808  7854 D Capacitor: Handling local request: http://localhost/2612.65fa6ebda188b95f.js
[2025-06-04 15:56:32] 06-04 20:20:40.386  7808  7876 D Capacitor: Handling local request: http://localhost/303.e27f52b200055458.js
[2025-06-04 15:56:32] 06-04 20:20:40.387  7808  7854 D Capacitor: Handling local request: http://localhost/8337.0d7e1a5c465486d2.js
[2025-06-04 15:56:32] 06-04 20:20:40.390  7808  7876 D Capacitor: Handling local request: http://localhost/3899.5a347a40176a6ed9.js
[2025-06-04 15:56:32] 06-04 20:20:40.411  7808  7875 D Capacitor: Handling local request: http://localhost/5241.d01a6eb7c287fe57.js
[2025-06-04 15:56:33] 06-04 20:20:40.428  7808  7875 D Capacitor: Handling local request: http://localhost/312.4f28c942009cb031.js
[2025-06-04 15:56:33] 06-04 20:20:40.429  7808  7876 D Capacitor: Handling local request: http://localhost/6956.d7a70c3fa8a35b5f.js
[2025-06-04 15:56:33] 06-04 20:20:40.433  7808  7854 D Capacitor: Handling local request: http://localhost/2455.fc3bb58cee6a130d.js
[2025-06-04 15:56:34] 06-04 20:20:40.433  7808  7875 D Capacitor: Handling local request: http://localhost/4940.9e6ebf85bfc99767.js
[2025-06-04 15:56:34] 06-04 20:20:40.434  7808  7859 D Capacitor: Handling local request: http://localhost/2157.3b976e68f963457f.js
[2025-06-04 15:56:34] 06-04 20:20:40.435  7808  7857 D Capacitor: Handling local request: http://localhost/1264.6b068f461e41ed94.js
[2025-06-04 15:56:35] 06-04 20:20:40.457  7808  7854 D Capacitor: Handling local request: http://localhost/3832.0f0980c12045e6a5.js
[2025-06-04 15:56:35] 06-04 20:20:40.458  7808  7854 D Capacitor: Handling local request: http://localhost/2643.ed481148dcb70f7b.js
[2025-06-04 15:56:35] 06-04 20:20:40.459  7808  7875 D Capacitor: Handling local request: http://localhost/6200.b47c0ba21a58b2b8.js
[2025-06-04 15:56:35] 06-04 20:20:40.461  7808  7854 D Capacitor: Handling local request: http://localhost/5395.ecc8dfb492b8cfbe.js
[2025-06-04 15:56:36] 06-04 20:20:40.462  7808  7875 D Capacitor: Handling local request: http://localhost/1775.a8cf170bb2be1cca.js
[2025-06-04 15:56:36] 06-04 20:20:40.464  7808  7854 D Capacitor: Handling local request: http://localhost/9484.9151899c7fdf581c.js
[2025-06-04 15:56:36] 06-04 20:20:40.466  7808  7857 D Capacitor: Handling local request: http://localhost/2043.3482352cc0010190.js
[2025-06-04 15:56:36] 06-04 20:20:40.538  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785130, pluginId: Device, methodName: getInfo
[2025-06-04 15:56:37] 06-04 20:20:40.538  7808  7808 V Capacitor: callback: 785130, pluginId: Device, methodName: getInfo, methodData: {}
[2025-06-04 15:56:37] 06-04 20:20:40.540  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785131, pluginId: Device, methodName: getId
[2025-06-04 15:56:37] 06-04 20:20:40.542  7808  7808 V Capacitor: callback: 785131, pluginId: Device, methodName: getId, methodData: {}
[2025-06-04 15:56:38] 06-04 20:20:40.544  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785132, pluginId: DeviceManager, methodName: requestInformation
[2025-06-04 15:56:38] 06-04 20:20:40.545  7808  7808 V Capacitor: callback: 785132, pluginId: DeviceManager, methodName: requestInformation, methodData: {}
[2025-06-04 15:56:38] 06-04 20:20:40.547  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785133, pluginId: UpdateManager, methodName: verifyStatus
[2025-06-04 15:56:38] 06-04 20:20:40.548  7808  7808 V Capacitor: callback: 785133, pluginId: UpdateManager, methodName: verifyStatus, methodData: {}
[2025-06-04 15:56:39] 06-04 20:20:40.550  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785134, pluginId: Preferences, methodName: get
[2025-06-04 15:56:39] 06-04 20:20:40.551  7808  7808 V Capacitor: callback: 785134, pluginId: Preferences, methodName: get, methodData: {"key":"version_update_status"}
[2025-06-04 15:56:39] 06-04 20:20:40.554  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785135, pluginId: Preferences, methodName: get
[2025-06-04 15:56:40] 06-04 20:20:40.555  7808  7808 V Capacitor: callback: 785135, pluginId: Preferences, methodName: get, methodData: {"key":"onboarding_status"}
[2025-06-04 15:56:40] 06-04 20:20:40.929  7808  7876 D Capacitor: Handling local request: http://localhost/4597.5c85152fb64819fb.js
[2025-06-04 15:56:40] 06-04 20:20:40.933  7808  7876 D Capacitor: Handling local request: http://localhost/5145.a5cccb1b924d28e1.js
[2025-06-04 15:56:41] 06-04 20:20:40.935  7808  7876 D Capacitor: Handling local request: http://localhost/7632.179a3719a632ca17.js
[2025-06-04 15:56:41] 06-04 20:20:40.942  7808  7876 D Capacitor: Handling local request: http://localhost/7432.0b4f934fda73ae52.js
[2025-06-04 15:56:41] 06-04 20:20:40.948  7808  7876 D Capacitor: Handling local request: http://localhost/7225.2a73d30b50bffb5b.js
[2025-06-04 15:56:42] 06-04 20:20:40.954  7808  7876 D Capacitor: Handling local request: http://localhost/7624.d00776862b500a02.js
[2025-06-04 15:56:42] 06-04 20:20:40.957  7808  7876 D Capacitor: Handling local request: http://localhost/9181.6cb02665bb860a44.js
[2025-06-04 15:56:42] 06-04 20:20:40.958  7808  7857 D Capacitor: Handling local request: http://localhost/8493.f1482e1b144f03d8.js
[2025-06-04 15:56:43] 06-04 20:20:41.093  7808  7857 D Capacitor: Handling local request: http://localhost/6374.a2f534adb88b0031.js
[2025-06-04 15:56:43] 06-04 20:20:41.100  7808  7857 D Capacitor: Handling local request: http://localhost/4709.c5e306a5d7d61bb2.js
[2025-06-04 15:56:43] 06-04 20:20:41.102  7808  7857 D Capacitor: Handling local request: http://localhost/9957.3814a5d94534cb37.js
[2025-06-04 15:56:44] 06-04 20:20:41.118  7808  7859 D Capacitor: Handling local request: http://localhost/9999.d1f18d76c304c04c.js
[2025-06-04 15:56:44] 06-04 20:20:41.120  7808  7859 D Capacitor: Handling local request: http://localhost/2485.a9865a2a179e70db.js
[2025-06-04 15:56:44] 06-04 20:20:41.123  7808  7857 D Capacitor: Handling local request: http://localhost/6935.d37b5618b1a1fe33.js
[2025-06-04 15:56:45] 06-04 20:20:41.151  7808  7857 D Capacitor: Handling local request: http://localhost/688.8fbf0e19e183877c.js
[2025-06-04 15:56:45] 06-04 20:20:41.171  7808  7859 D Capacitor: Handling local request: http://localhost/9609.d9ebb216cabfe7cc.js
[2025-06-04 15:56:45] 06-04 20:20:41.176  7808  7859 D Capacitor: Handling local request: http://localhost/5055.7f685a15b5baf685.js
[2025-06-04 15:56:45] 06-04 20:20:41.186  7808  7859 D Capacitor: Handling local request: http://localhost/5773.33a758aa8f0e7ce3.js
[2025-06-04 15:56:46] 06-04 20:20:41.190  7808  7859 D Capacitor: Handling local request: http://localhost/8535.0c916bd24654a3cd.js
[2025-06-04 15:56:46] 06-04 20:20:41.195  7808  7857 D Capacitor: Handling local request: http://localhost/9804.0b089f66051f6c2b.js
[2025-06-04 15:56:46] 06-04 20:20:41.202  7808  7857 D Capacitor: Handling local request: http://localhost/7233.aa1f332db05df32f.js
[2025-06-04 15:56:47] 06-04 20:20:41.204  7808  7857 D Capacitor: Handling local request: http://localhost/2004.7cc5935b440beeab.js
[2025-06-04 15:56:47] 06-04 20:20:41.213  7808  7859 D Capacitor: Handling local request: http://localhost/7536.73b6da0343e6b528.js
[2025-06-04 15:56:47] 06-04 20:20:41.214  7808  7857 D Capacitor: Handling local request: http://localhost/4179.b557ca6f2e28ad75.js
[2025-06-04 15:56:47] 06-04 20:20:41.220  7808  7875 D Capacitor: Handling local request: http://localhost/1368.537a2c28bf9f9915.js
[2025-06-04 15:56:48] 06-04 20:20:41.221  7808  7876 D Capacitor: Handling local request: http://localhost/6149.4ead25bb7c9b5a66.js
[2025-06-04 15:56:48] 06-04 20:20:41.225  7808  7857 D Capacitor: Handling local request: http://localhost/2315.888e64acf65771f9.js
[2025-06-04 15:56:48] 06-04 20:20:41.237  7808  7854 D Capacitor: Handling local request: http://localhost/7404.9acd426f035adfc1.js
[2025-06-04 15:56:49] 06-04 20:20:41.243  7808  7875 D Capacitor: Handling local request: http://localhost/3637.92dffc731b479f6c.js
[2025-06-04 15:56:49] 06-04 20:20:41.428  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 122 - Msg: 🚀 MTH: triggerAudienceCheck isCognitoReady true
[2025-06-04 15:56:49] 06-04 20:20:41.428  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 122 - Msg: 🚀 MTH: triggerAudienceCheck uuid undefined
[2025-06-04 15:56:50] 06-04 20:20:41.431  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 122 - Msg: 🚀 MTH: triggerAudienceCheck env undefined
[2025-06-04 15:56:50] 06-04 20:20:41.432  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 125 - Msg: 🚀 MTH Pre-Enrolled ReCAPTCHA Success [object Object]
[2025-06-04 15:56:50] 06-04 20:20:41.811  7808  7875 D Capacitor: Handling local request: http://localhost/3672.dfda60cd135af174.js
[2025-06-04 15:56:50] 06-04 20:20:41.814  7808  7875 D Capacitor: Handling local request: http://localhost/3544.d184ca4ae7cc99a2.js
[2025-06-04 15:56:51] 06-04 20:20:42.116  7808  7857 D Capacitor: Handling local request: http://localhost/assets/favicon.ico
[2025-06-04 15:56:51] 06-04 20:20:44.492  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4255 - Msg: 1. Data------------> [object Object]
[2025-06-04 15:56:51] 06-04 20:20:44.497  7808  7808 W Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4215 - Msg: 2. send to: -------------> [object Object]
[2025-06-04 15:56:52] 06-04 20:20:44.591  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/login
[2025-06-04 15:56:52] 06-04 20:20:44.592  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 551 - Msg: location changed carga de estilos => /authentication/login
[2025-06-04 15:56:52] 06-04 20:20:44.593  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 907 - Msg: location changed carga de oferta => /authentication/login
[2025-06-04 15:56:53] 06-04 20:20:44.597  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 2015 - Msg: location changed carga form1 => /authentication/login
[2025-06-04 15:56:53] 06-04 20:20:44.598  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4165 - Msg: location changed carga form2 => /authentication/login
[2025-06-04 15:56:53] 06-04 20:20:44.599  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/login
[2025-06-04 15:56:53] 06-04 20:20:44.599  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/login
[2025-06-04 15:56:54] 06-04 20:20:44.744  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.sync.js - Line 294 - Msg: at_property
[2025-06-04 15:56:54] 06-04 20:20:44.843  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785136, pluginId: Preferences, methodName: get
[2025-06-04 15:56:54] 06-04 20:20:44.844  7808  7808 V Capacitor: callback: 785136, pluginId: Preferences, methodName: get, methodData: {"key":"customer"}
[2025-06-04 15:56:55] 06-04 20:20:44.845  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785137, pluginId: NativeBiometric, methodName: isAvailable
[2025-06-04 15:56:55] 06-04 20:20:44.845  7808  7808 V Capacitor: callback: 785137, pluginId: NativeBiometric, methodName: isAvailable, methodData: {}
[2025-06-04 15:56:55] 06-04 20:20:44.847  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785138, pluginId: Preferences, methodName: get
[2025-06-04 15:56:56] 06-04 20:20:44.847  7808  7808 V Capacitor: callback: 785138, pluginId: Preferences, methodName: get, methodData: {"key":"biometric_linked"}
[2025-06-04 15:56:56] 06-04 20:20:44.851  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785139, pluginId: OnespanBinding, methodName: getFingerprint
[2025-06-04 15:56:56] 06-04 20:20:44.851  7808  7808 V Capacitor: callback: 785139, pluginId: OnespanBinding, methodName: getFingerprint, methodData: {"salt":"8khYqw8tgje8bdp2Mj4ZfC6FYG0K16Jxl7dLIOKKD4mHd8RT1DfnPO1dIGR5ZYrf","androidIdType":2}
[2025-06-04 15:56:57] 06-04 20:20:44.852  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785140, pluginId: Preferences, methodName: get
[2025-06-04 15:56:57] 06-04 20:20:44.853  7808  7808 V Capacitor: callback: 785140, pluginId: Preferences, methodName: get, methodData: {"key":"customer_silent_migrations"}
[2025-06-04 15:56:57] 06-04 20:20:44.855  7808  7808 I Capacitor/Console: File: http://localhost/2653.337a4456ae108e2e.js - Line 1 - Msg: ConfigCat - INFO - [5000] Evaluating 'visible-tag-aval-login'
[2025-06-04 15:56:57] 06-04 20:20:44.855  7808  7808 I Capacitor/Console:   Returning 'true'.
[2025-06-04 15:56:58] 06-04 20:20:44.856  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 108 - Msg: delete cookies document.location.pathname :>>  /authentication/login
[2025-06-04 15:56:58] 06-04 20:20:44.858  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 108 - Msg: delete cookies starting...
[2025-06-04 15:56:58] 06-04 20:20:44.859  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 106 - Msg: deleteCookies targetCampaingValue :>>  undefined
[2025-06-04 15:56:59] 06-04 20:20:44.864  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 108 - Msg: cookieName / storage deleted :>>  BB
[2025-06-04 15:56:59] 06-04 20:20:44.869  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 108 - Msg: cookieName / storage deleted :>>  PB_OCC_CC_DATA
[2025-06-04 15:56:59] 06-04 20:20:44.873  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 108 - Msg: cookieName / storage deleted :>>  PB_OCC_CC_DATAFORM
[2025-06-04 15:56:59] 06-04 20:20:44.873  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 108 - Msg: Las cookies (android) / localStorage (ios) han sido eliminadas : BB, PB_OCC_CC_DATA, PB_OCC_CC_DATAFORM
[2025-06-04 15:57:00] 06-04 20:20:44.875  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 111 - Msg: onUrlChange product_0CC or SecFormDataCard_ADL deleted
[2025-06-04 15:57:00] 06-04 20:20:44.878  7808  7857 D Capacitor: Handling local request: http://localhost/assets/shared/logos/modals/search-result-none.svg
[2025-06-04 15:57:00] 06-04 20:20:44.883  7808  7857 D Capacitor: Handling local request: http://localhost/assets/shared/logos/banco-occidente.svg
[2025-06-04 15:57:01] 06-04 20:20:44.899  7808  7875 D Capacitor: Handling local request: http://localhost/assets/shared/fonts/poppins/Poppins-Bold.woff2
[2025-06-04 15:57:01] 06-04 20:20:44.938  7808  7875 D Capacitor: Handling local request: http://localhost/assets/favicon.ico
[2025-06-04 15:57:01] 06-04 20:20:44.960  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785141, pluginId: OneSpanSecureStorage, methodName: connect
[2025-06-04 15:57:01] 06-04 20:20:44.961  7808  7808 V Capacitor: callback: 785141, pluginId: OneSpanSecureStorage, methodName: connect, methodData: {"storageName":"bocc-mb-sec-storage","fingerPrint":"509F0A716566EA231692CD53082222EC80F7F1DFA010DEB57982C1B1F99CC842","iterationNumber":8000}
[2025-06-04 15:57:02] 06-04 20:20:45.155  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.sync.js - Line 294 - Msg: at_property
[2025-06-04 15:57:02] 06-04 20:20:45.176  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.sync.js - Line 294 - Msg: at_property
[2025-06-04 15:57:02] 06-04 20:20:45.305  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785142, pluginId: OneSpanSecureStorage, methodName: getString
[2025-06-04 15:57:03] 06-04 20:20:45.306  7808  7808 V Capacitor: callback: 785142, pluginId: OneSpanSecureStorage, methodName: getString, methodData: {"forKey":"token"}
[2025-06-04 15:57:03] 06-04 20:20:45.308  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785143, pluginId: OneSpanSecureStorage, methodName: getString
[2025-06-04 15:57:03] 06-04 20:20:45.308  7808  7808 V Capacitor: callback: 785143, pluginId: OneSpanSecureStorage, methodName: getString, methodData: {"forKey":"tagsAval"}
[2025-06-04 15:57:04] 06-04 20:20:45.311  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785144, pluginId: OneSpanSecureStorage, methodName: getString
[2025-06-04 15:57:04] 06-04 20:20:45.312  7808  7808 V Capacitor: callback: 785144, pluginId: OneSpanSecureStorage, methodName: getString, methodData: {"forKey":"tagAval"}
[2025-06-04 15:57:04] 06-04 20:20:45.316  7808  7894 D Capacitor: Sending plugin error: {"save":false,"callbackId":"785144","pluginId":"OneSpanSecureStorage","methodName":"getString","success":false,"error":{"message":"Failed to get a string from Secure Storage, Storage does not contains requested key"}}
[2025-06-04 15:57:05] 06-04 20:20:45.346  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785145, pluginId: OneSpanSecureStorage, methodName: getString
[2025-06-04 15:57:05] 06-04 20:20:45.346  7808  7808 V Capacitor: callback: 785145, pluginId: OneSpanSecureStorage, methodName: getString, methodData: {"forKey":"token"}
[2025-06-04 15:57:05] 06-04 20:20:45.923  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785146, pluginId: OneSpanSecureStorage, methodName: contains
[2025-06-04 15:57:05] 06-04 20:20:45.923  7808  7808 V Capacitor: callback: 785146, pluginId: OneSpanSecureStorage, methodName: contains, methodData: {"forKey":"token"}
[2025-06-04 15:57:06] 06-04 20:20:45.926  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785147, pluginId: OneSpanSecureStorage, methodName: contains
[2025-06-04 15:57:06] 06-04 20:20:45.928  7808  7808 V Capacitor: callback: 785147, pluginId: OneSpanSecureStorage, methodName: contains, methodData: {"forKey":"tokenExp"}
[2025-06-04 15:57:06] 06-04 20:20:45.938  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785148, pluginId: OneSpanSecureStorage, methodName: remove
[2025-06-04 15:57:07] 06-04 20:20:45.938  7808  7808 V Capacitor: callback: 785148, pluginId: OneSpanSecureStorage, methodName: remove, methodData: {"forKey":"token"}
[2025-06-04 15:57:07] 06-04 20:20:45.941  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785149, pluginId: OneSpanSecureStorage, methodName: remove
[2025-06-04 15:57:07] 06-04 20:20:45.942  7808  7808 V Capacitor: callback: 785149, pluginId: OneSpanSecureStorage, methodName: remove, methodData: {"forKey":"tokenExp"}
[2025-06-04 15:57:07] 06-04 20:20:45.952  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785150, pluginId: OneSpanSecureStorage, methodName: write
[2025-06-04 15:57:08] 06-04 20:20:45.953  7808  7808 V Capacitor: callback: 785150, pluginId: OneSpanSecureStorage, methodName: write, methodData: {}
[2025-06-04 15:57:08] 06-04 20:20:45.954  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785151, pluginId: OneSpanSecureStorage, methodName: write
[2025-06-04 15:57:08] 06-04 20:20:45.955  7808  7808 V Capacitor: callback: 785151, pluginId: OneSpanSecureStorage, methodName: write, methodData: {}
[2025-06-04 15:57:09] 06-04 20:20:46.328  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785152, pluginId: Preferences, methodName: remove
[2025-06-04 15:57:09] 06-04 20:20:46.328  7808  7808 V Capacitor: callback: 785152, pluginId: Preferences, methodName: remove, methodData: {"key":"customer_session"}
[2025-06-04 15:57:09] 06-04 20:20:46.333  7808  7808 I Capacitor/Console: File:  - Line 353 - Msg: undefined
[2025-06-04 15:57:09] 06-04 20:20:47.895  7808  7808 I Capacitor/Console: File: http://localhost/3850.2ea594ed81c9a2a5.js - Line 18 - Msg: Download the Apollo DevTools for a better development experience: %s https://chrome.google.com/webstore/detail/apollo-client-developer-t/jdkknkkbebbapilgoeccciglkfbmbnfm
[2025-06-04 15:57:10] 06-04 20:20:50.193  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4255 - Msg: 1. Data------------> [object Object]
[2025-06-04 15:57:10] 06-04 20:20:50.419  7808  7875 D Capacitor: Handling local request: http://localhost/assets/favicon.ico
[2025-06-04 15:57:10] 06-04 20:20:52.197  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4255 - Msg: 1. Data------------> [object Object]
[2025-06-04 15:57:11] 06-04 20:20:53.613  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4255 - Msg: 1. Data------------> [object Object]
[2025-06-04 15:57:11] 06-04 20:20:53.613  7808  7808 W Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4215 - Msg: 2. send to: -------------> [object Object]
[2025-06-04 15:57:12] 06-04 20:20:53.671  7808  7808 I Capacitor/Console: File: http://localhost/2653.337a4456ae108e2e.js - Line 1 - Msg: ConfigCat - INFO - [5000] Evaluating 'new-login'
[2025-06-04 15:57:12] 06-04 20:20:53.671  7808  7808 I Capacitor/Console:   Returning 'false'.
[2025-06-04 15:57:12] 06-04 20:20:53.734  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 78 - Msg: generateUserID typeDoc :>>  cc 
[2025-06-04 15:57:13] 06-04 20:20:53.734  7808  7808 I Capacitor/Console: numDoc :>>  91539937
[2025-06-04 15:57:13] 06-04 20:20:53.735  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 151 - Msg: Login Button clicked! addListenerBBCookie Cookie BB with adlID :>>  37/55698687234390BB 
[2025-06-04 15:57:13] 06-04 20:20:53.735  7808  7808 I Capacitor/Console: type_doc:  CC num_doc 91539937
[2025-06-04 15:57:14] 06-04 20:20:53.739  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 126 - Msg: 🚀 MTH: Audiences API init
[2025-06-04 15:57:14] 06-04 20:20:53.851  7808  7875 D Capacitor: Handling local request: http://localhost/assets/favicon.ico
[2025-06-04 15:57:14] 06-04 20:20:54.238  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 131 - Msg: 🚀 MTH: Audiences API success
[2025-06-04 15:57:15] 06-04 20:20:54.270  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 131 - Msg: 🚀 MTH: data [object Object]
[2025-06-04 15:57:15] 06-04 20:20:54.273  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 116 - Msg: 🚀 MTH: setAudience undefined
[2025-06-04 15:57:15] 06-04 20:20:54.276  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 117 - Msg: 🚀 MTH: triggerMthSpaces isAudienceReady true
[2025-06-04 15:57:15] 06-04 20:20:54.279  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 117 - Msg: 🚀 MTH: triggerMthSpaces subscribers.length 0
[2025-06-04 15:57:16] 06-04 20:20:58.353  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785153, pluginId: OneSpanSecureStorage, methodName: putString
[2025-06-04 15:57:16] 06-04 20:20:58.353  7808  7808 V Capacitor: callback: 785153, pluginId: OneSpanSecureStorage, methodName: putString, methodData: {"forKey":"token","forValue":"**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}
[2025-06-04 15:57:17] 06-04 20:20:58.387  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785154, pluginId: OneSpanSecureStorage, methodName: getString
[2025-06-04 15:57:17] 06-04 20:20:58.387  7808  7808 V Capacitor: callback: 785154, pluginId: OneSpanSecureStorage, methodName: getString, methodData: {"forKey":"token"}
[2025-06-04 15:57:17] 06-04 20:20:58.394  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785155, pluginId: OneSpanSecureStorage, methodName: write
[2025-06-04 15:57:17] 06-04 20:20:58.394  7808  7808 V Capacitor: callback: 785155, pluginId: OneSpanSecureStorage, methodName: write, methodData: {}
[2025-06-04 15:57:18] 06-04 20:20:58.576  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785156, pluginId: OneSpanSecureStorage, methodName: putString
[2025-06-04 15:57:18] 06-04 20:20:58.576  7808  7808 V Capacitor: callback: 785156, pluginId: OneSpanSecureStorage, methodName: putString, methodData: {"forKey":"tokenExp","forValue":"1749072058000"}
[2025-06-04 15:57:18] 06-04 20:20:58.586  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785157, pluginId: OneSpanSecureStorage, methodName: write
[2025-06-04 15:57:19] 06-04 20:20:58.586  7808  7808 V Capacitor: callback: 785157, pluginId: OneSpanSecureStorage, methodName: write, methodData: {}
[2025-06-04 15:57:19] 06-04 20:21:07.180  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785158, pluginId: Preferences, methodName: get
[2025-06-04 15:57:20] 06-04 20:21:07.180  7808  7808 V Capacitor: callback: 785158, pluginId: Preferences, methodName: get, methodData: {"key":"customer_silent_migration"}
[2025-06-04 15:57:20] 06-04 20:21:07.207  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785159, pluginId: OneSpanSecureStorage, methodName: putString
[2025-06-04 15:57:20] 06-04 20:21:07.207  7808  7808 V Capacitor: callback: 785159, pluginId: OneSpanSecureStorage, methodName: putString, methodData: {"forKey":"tmpPassword","forValue":"12312123123"}
[2025-06-04 15:57:21] 06-04 20:21:07.219  7808  7808 E Capacitor/Console: File: http://localhost/4650.99a220314a61f83a.js - Line 1 - Msg: ERROR TypeError: Cannot read properties of undefined (reading 'manager')
[2025-06-04 15:57:21] 06-04 20:21:07.221  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785160, pluginId: OneSpanSecureStorage, methodName: write
[2025-06-04 15:57:21] 06-04 20:21:07.222  7808  7808 V Capacitor: callback: 785160, pluginId: OneSpanSecureStorage, methodName: write, methodData: {}
[2025-06-04 15:57:21] 06-04 20:21:07.388  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785161, pluginId: OneSpanSecureStorage, methodName: putString
[2025-06-04 15:57:22] 06-04 20:21:07.389  7808  7808 V Capacitor: callback: 785161, pluginId: OneSpanSecureStorage, methodName: putString, methodData: {"forKey":"biometricUsertype","forValue":"CC"}
[2025-06-04 15:57:22] 06-04 20:21:07.390  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785162, pluginId: OneSpanSecureStorage, methodName: putString
[2025-06-04 15:57:22] 06-04 20:21:07.391  7808  7808 V Capacitor: callback: 785162, pluginId: OneSpanSecureStorage, methodName: putString, methodData: {"forKey":"biometricUsername","forValue":"91539937"}
[2025-06-04 15:57:23] 06-04 20:21:07.393  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785163, pluginId: OneSpanSecureStorage, methodName: putString
[2025-06-04 15:57:23] 06-04 20:21:07.394  7808  7808 V Capacitor: callback: 785163, pluginId: OneSpanSecureStorage, methodName: putString, methodData: {"forKey":"biometricPassword","forValue":"12312123123"}
[2025-06-04 15:57:23] 06-04 20:21:07.404  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785164, pluginId: OneSpanSecureStorage, methodName: write
[2025-06-04 15:57:24] 06-04 20:21:07.405  7808  7808 V Capacitor: callback: 785164, pluginId: OneSpanSecureStorage, methodName: write, methodData: {}
[2025-06-04 15:57:24] 06-04 20:21:07.417  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785165, pluginId: OneSpanSecureStorage, methodName: write
[2025-06-04 15:57:24] 06-04 20:21:07.417  7808  7808 V Capacitor: callback: 785165, pluginId: OneSpanSecureStorage, methodName: write, methodData: {}
[2025-06-04 15:57:25] 06-04 20:21:07.419  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785166, pluginId: OneSpanSecureStorage, methodName: write
[2025-06-04 15:57:25] 06-04 20:21:07.420  7808  7808 V Capacitor: callback: 785166, pluginId: OneSpanSecureStorage, methodName: write, methodData: {}
[2025-06-04 15:57:25] 06-04 20:21:07.884  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 155 - Msg: entro utag.spa Triggerview => /home/<USER>/products
[2025-06-04 15:57:26] 06-04 20:21:07.890  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 156 - Msg: visitor :>>  [object Object]
[2025-06-04 15:57:26] 06-04 20:21:07.891  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 157 - Msg: visitAuth :>> {"id":"37/55698687234390BB","authState":1}
[2025-06-04 15:57:26] 06-04 20:21:07.950  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /home/<USER>/products
[2025-06-04 15:57:26] 06-04 20:21:07.950  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: Mutation Carga de Funciones.... IN  location.href = http://localhost/authentication/login
[2025-06-04 15:57:27] 06-04 20:21:07.951  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: inicio extensión PB Compra de cartera
[2025-06-04 15:57:27] 06-04 20:21:07.954  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 551 - Msg: End BM Compra de Cartera - Carga de funciones
[2025-06-04 15:57:27] 06-04 20:21:07.954  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 551 - Msg: location changed carga de estilos => /home/<USER>/products
[2025-06-04 15:57:28] 06-04 20:21:07.959  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 879 - Msg: End load extensión PB Compra de Cartera - Carga de estilos
[2025-06-04 15:57:28] 06-04 20:21:07.960  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 907 - Msg: location changed carga de oferta => /home/<USER>/products
[2025-06-04 15:57:28] 06-04 20:21:07.961  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 924 - Msg: End... BM Compra de Cartera - Carga de Oferta
[2025-06-04 15:57:29] 06-04 20:21:07.962  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 2015 - Msg: location changed carga form1 => /home/<USER>/products
[2025-06-04 15:57:29] 06-04 20:21:07.962  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 2019 - Msg: Cargando form 1
[2025-06-04 15:57:29] 06-04 20:21:07.963  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 2138 - Msg: End BM Compra de Cartera-Carga Form 1
[2025-06-04 15:57:30] 06-04 20:21:07.964  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4165 - Msg: location changed carga form2 => /home/<USER>/products
[2025-06-04 15:57:30] 06-04 20:21:07.965  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4165 - Msg: Cargando form 2
[2025-06-04 15:57:30] 06-04 20:21:07.965  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4177 - Msg: End BM Compra de Cartera - Carga Form 1
[2025-06-04 15:57:31] 06-04 20:21:07.966  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /home/<USER>/products
[2025-06-04 15:57:31] 06-04 20:21:07.967  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: Mutation Carga de Funciones.... IN  location.href = http://localhost/authentication/login
[2025-06-04 15:57:31] 06-04 20:21:07.968  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: inicio extensión PB Compra de cartera
[2025-06-04 15:57:32] 06-04 20:21:07.969  7808  7808 E Capacitor/Console: File: http://localhost/7423.ec3d02d0e1698874.js - Line 1 - Msg: SyntaxError: Identifier 'regexIsNumber_ADL' has already been declared
[2025-06-04 15:57:32] 06-04 20:21:07.969  7808  7808 E Capacitor: JavaScript Error: {"type":"js.error","error":{"message":"Uncaught SyntaxError: Identifier 'regexIsNumber_ADL' has already been declared","url":"http://localhost/authentication/login","line":1,"col":1,"errorObject":"{}"}}
[2025-06-04 15:57:32] 06-04 20:21:07.970  7808  7808 E Capacitor/Console: File: http://localhost/authentication/login - Line 1 - Msg: Uncaught SyntaxError: Identifier 'regexIsNumber_ADL' has already been declared
[2025-06-04 15:57:33] 06-04 20:21:07.971  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 551 - Msg: End BM Compra de Cartera - Carga de funciones
[2025-06-04 15:57:33] 06-04 20:21:07.971  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /home/<USER>/products
[2025-06-04 15:57:33] 06-04 20:21:07.971  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: Mutation Carga de Funciones.... IN  location.href = http://localhost/authentication/login
[2025-06-04 15:57:34] 06-04 20:21:07.972  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: inicio extensión PB Compra de cartera
[2025-06-04 15:57:34] 06-04 20:21:07.973  7808  7808 E Capacitor/Console: File: http://localhost/7423.ec3d02d0e1698874.js - Line 1 - Msg: SyntaxError: Identifier 'regexIsNumber_ADL' has already been declared
[2025-06-04 15:57:34] 06-04 20:21:07.975  7808  7808 E Capacitor: JavaScript Error: {"type":"js.error","error":{"message":"Uncaught SyntaxError: Identifier 'regexIsNumber_ADL' has already been declared","url":"http://localhost/authentication/login","line":1,"col":1,"errorObject":"{}"}}
[2025-06-04 15:57:34] 06-04 20:21:07.976  7808  7808 E Capacitor/Console: File: http://localhost/authentication/login - Line 1 - Msg: Uncaught SyntaxError: Identifier 'regexIsNumber_ADL' has already been declared
[2025-06-04 15:57:35] 06-04 20:21:07.978  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 551 - Msg: End BM Compra de Cartera - Carga de funciones
[2025-06-04 15:57:35] 06-04 20:21:07.979  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /home/<USER>/products
[2025-06-04 15:57:35] 06-04 20:21:07.979  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: Mutation Carga de Funciones.... IN  location.href = http://localhost/authentication/login
[2025-06-04 15:57:36] 06-04 20:21:07.979  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: inicio extensión PB Compra de cartera
[2025-06-04 15:57:36] 06-04 20:21:07.981  7808  7808 E Capacitor/Console: File: http://localhost/7423.ec3d02d0e1698874.js - Line 1 - Msg: SyntaxError: Identifier 'regexIsNumber_ADL' has already been declared
[2025-06-04 15:57:36] 06-04 20:21:07.982  7808  7808 E Capacitor: JavaScript Error: {"type":"js.error","error":{"message":"Uncaught SyntaxError: Identifier 'regexIsNumber_ADL' has already been declared","url":"http://localhost/authentication/login","line":1,"col":1,"errorObject":"{}"}}
[2025-06-04 15:57:36] 06-04 20:21:07.982  7808  7808 E Capacitor/Console: File: http://localhost/authentication/login - Line 1 - Msg: Uncaught SyntaxError: Identifier 'regexIsNumber_ADL' has already been declared
[2025-06-04 15:57:37] 06-04 20:21:07.983  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 551 - Msg: End BM Compra de Cartera - Carga de funciones
[2025-06-04 15:57:37] 06-04 20:21:07.984  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /home/<USER>/products
[2025-06-04 15:57:37] 06-04 20:21:07.984  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: Mutation Carga de Funciones.... IN  location.href = http://localhost/authentication/login
[2025-06-04 15:57:38] 06-04 20:21:07.985  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: inicio extensión PB Compra de cartera
[2025-06-04 15:57:38] 06-04 20:21:07.986  7808  7808 E Capacitor/Console: File: http://localhost/7423.ec3d02d0e1698874.js - Line 1 - Msg: SyntaxError: Identifier 'regexIsNumber_ADL' has already been declared
[2025-06-04 15:57:38] 06-04 20:21:07.987  7808  7808 E Capacitor: JavaScript Error: {"type":"js.error","error":{"message":"Uncaught SyntaxError: Identifier 'regexIsNumber_ADL' has already been declared","url":"http://localhost/authentication/login","line":1,"col":1,"errorObject":"{}"}}
[2025-06-04 15:57:39] 06-04 20:21:07.987  7808  7808 E Capacitor/Console: File: http://localhost/authentication/login - Line 1 - Msg: Uncaught SyntaxError: Identifier 'regexIsNumber_ADL' has already been declared
[2025-06-04 15:57:39] 06-04 20:21:07.987  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 551 - Msg: End BM Compra de Cartera - Carga de funciones
[2025-06-04 15:57:39] 06-04 20:21:07.989  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /home/<USER>/products
[2025-06-04 15:57:39] 06-04 20:21:07.990  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: Mutation Carga de Funciones.... IN  location.href = http://localhost/authentication/login
[2025-06-04 15:57:40] 06-04 20:21:07.990  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: inicio extensión PB Compra de cartera
[2025-06-04 15:57:40] 06-04 20:21:07.990  7808  7808 E Capacitor/Console: File: http://localhost/7423.ec3d02d0e1698874.js - Line 1 - Msg: SyntaxError: Identifier 'regexIsNumber_ADL' has already been declared
[2025-06-04 15:57:40] 06-04 20:21:07.991  7808  7808 E Capacitor: JavaScript Error: {"type":"js.error","error":{"message":"Uncaught SyntaxError: Identifier 'regexIsNumber_ADL' has already been declared","url":"http://localhost/authentication/login","line":1,"col":1,"errorObject":"{}"}}
[2025-06-04 15:57:41] 06-04 20:21:07.992  7808  7808 E Capacitor/Console: File: http://localhost/authentication/login - Line 1 - Msg: Uncaught SyntaxError: Identifier 'regexIsNumber_ADL' has already been declared
[2025-06-04 15:57:41] 06-04 20:21:07.993  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 551 - Msg: End BM Compra de Cartera - Carga de funciones
[2025-06-04 15:57:41] 06-04 20:21:07.994  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /home/<USER>/products
[2025-06-04 15:57:41] 06-04 20:21:07.995  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: Mutation Carga de Funciones.... IN  location.href = http://localhost/authentication/login
[2025-06-04 15:57:42] 06-04 20:21:07.996  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: inicio extensión PB Compra de cartera
[2025-06-04 15:57:42] 06-04 20:21:07.996  7808  7808 E Capacitor/Console: File: http://localhost/7423.ec3d02d0e1698874.js - Line 1 - Msg: SyntaxError: Identifier 'regexIsNumber_ADL' has already been declared
[2025-06-04 15:57:42] 06-04 20:21:07.998  7808  7808 E Capacitor: JavaScript Error: {"type":"js.error","error":{"message":"Uncaught SyntaxError: Identifier 'regexIsNumber_ADL' has already been declared","url":"http://localhost/authentication/login","line":1,"col":1,"errorObject":"{}"}}
[2025-06-04 15:57:43] 06-04 20:21:07.999  7808  7808 E Capacitor/Console: File: http://localhost/authentication/login - Line 1 - Msg: Uncaught SyntaxError: Identifier 'regexIsNumber_ADL' has already been declared
[2025-06-04 15:57:43] 06-04 20:21:07.999  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 551 - Msg: End BM Compra de Cartera - Carga de funciones
[2025-06-04 15:57:43] 06-04 20:21:08.093  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.sync.js - Line 294 - Msg: at_property
[2025-06-04 15:57:44] 06-04 20:21:08.111  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.sync.js - Line 294 - Msg: at_property
[2025-06-04 15:57:44] 06-04 20:21:08.234  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785167, pluginId: Preferences, methodName: get
[2025-06-04 15:57:44] 06-04 20:21:08.234  7808  7808 V Capacitor: callback: 785167, pluginId: Preferences, methodName: get, methodData: {"key":"customer_first_login"}
[2025-06-04 15:57:44] 06-04 20:21:08.237  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785168, pluginId: Preferences, methodName: get
[2025-06-04 15:57:45] 06-04 20:21:08.237  7808  7808 V Capacitor: callback: 785168, pluginId: Preferences, methodName: get, methodData: {"key":"biometric_linked"}
[2025-06-04 15:57:45] 06-04 20:21:08.239  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785169, pluginId: OneSpanSecureStorage, methodName: getString
[2025-06-04 15:57:45] 06-04 20:21:08.239  7808  7808 V Capacitor: callback: 785169, pluginId: OneSpanSecureStorage, methodName: getString, methodData: {"forKey":"token"}
[2025-06-04 15:57:46] 06-04 20:21:08.241  7808  7808 I Capacitor/Console: File: http://localhost/2653.337a4456ae108e2e.js - Line 1 - Msg: ConfigCat - INFO - [5000] Evaluating 'aval-adviser'
[2025-06-04 15:57:46] 06-04 20:21:08.241  7808  7808 I Capacitor/Console:   Returning 'true'.
[2025-06-04 15:57:46] 06-04 20:21:08.241  7808  7808 I Capacitor/Console: File: http://localhost/2653.337a4456ae108e2e.js - Line 1 - Msg: ConfigCat - INFO - [5000] Evaluating 'tuplus'
[2025-06-04 15:57:47] 06-04 20:21:08.241  7808  7808 I Capacitor/Console:   Returning 'true'.
[2025-06-04 15:57:47] 06-04 20:21:08.242  7808  7808 I Capacitor/Console: File: http://localhost/2653.337a4456ae108e2e.js - Line 1 - Msg: ConfigCat - INFO - [5000] Evaluating 'remittains'
[2025-06-04 15:57:47] 06-04 20:21:08.242  7808  7808 I Capacitor/Console:   Returning 'true'.
[2025-06-04 15:57:48] 06-04 20:21:08.242  7808  7808 I Capacitor/Console: File: http://localhost/2653.337a4456ae108e2e.js - Line 1 - Msg: ConfigCat - INFO - [5000] Evaluating 'enabled-spi'
[2025-06-04 15:57:48] 06-04 20:21:08.242  7808  7808 I Capacitor/Console:   Returning 'true'.
[2025-06-04 15:57:48] 06-04 20:21:08.245  7808  7808 I Capacitor/Console: File: http://localhost/2653.337a4456ae108e2e.js - Line 1 - Msg: ConfigCat - INFO - [5000] Evaluating 'caps'
[2025-06-04 15:57:48] 06-04 20:21:08.245  7808  7808 I Capacitor/Console:   Returning 'true'.
[2025-06-04 15:57:49] 06-04 20:21:08.246  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 108 - Msg: delete cookies document.location.pathname :>>  /home/<USER>/products
[2025-06-04 15:57:49] 06-04 20:21:08.260  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 879 - Msg: .mbo-application-header__menu found!
[2025-06-04 15:57:49] 06-04 20:21:08.261  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 907 - Msg: configurando menu
[2025-06-04 15:57:50] 06-04 20:21:08.267  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 907 - Msg: callback .mbo-products-carousel__content mbo-product-card-account Element exists!
[2025-06-04 15:57:50] 06-04 20:21:08.268  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 907 - Msg: product_list:
[2025-06-04 15:57:50] 06-04 20:21:08.268  7808  7808 I Capacitor/Console:  [object HTMLDivElement]
[2025-06-04 15:57:51] 06-04 20:21:08.271  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 907 - Msg: product_list2:
[2025-06-04 15:57:51] 06-04 20:21:08.271  7808  7808 I Capacitor/Console:  [object HTMLElement]
[2025-06-04 15:57:51] 06-04 20:21:08.272  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 907 - Msg: product_1:
[2025-06-04 15:57:51] 06-04 20:21:08.272  7808  7808 I Capacitor/Console:  null
[2025-06-04 15:57:52] 06-04 20:21:08.273  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 907 - Msg: Va a esperar la cookie PB_OCC_CC_DATA
[2025-06-04 15:57:52] 06-04 20:21:08.274  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4177 - Msg: End BM Compra de Cartera - Carga Form 2
[2025-06-04 15:57:52] 06-04 20:21:08.278  7808  7875 D Capacitor: Handling local request: http://localhost/assets/shared/logos/occidente-white.svg
[2025-06-04 15:57:53] 06-04 20:21:08.281  7808  7875 D Capacitor: Handling local request: http://localhost/assets/shared/icons/notifications.svg
[2025-06-04 15:57:53] 06-04 20:21:08.282  7808  7858 D Capacitor: Handling local request: http://localhost/assets/shared/logos/occidente-sidenav.svg
[2025-06-04 15:57:53] 06-04 20:21:08.284  7808  7875 D Capacitor: Handling local request: http://localhost/assets/shared/logos/aval.svg
[2025-06-04 15:57:54] 06-04 20:21:08.286  7808  7857 D Capacitor: Handling local request: http://localhost/assets/shared/logos/fiduoccidente.svg
[2025-06-04 15:57:54] 06-04 20:21:08.287  7808  7858 D Capacitor: Handling local request: http://localhost/assets/shared/logos/aval-banks.svg
[2025-06-04 15:57:54] 06-04 20:21:08.288  7808  7876 D Capacitor: Handling local request: http://localhost/assets/shared/logos/tuplus.svg
[2025-06-04 15:57:54] 06-04 20:21:08.316  7808  7857 D Capacitor: Handling local request: http://localhost/assets/favicon.ico
[2025-06-04 15:57:55] 06-04 20:21:08.322  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785170, pluginId: NativeBiometric, methodName: isAvailable
[2025-06-04 15:57:55] 06-04 20:21:08.322  7808  7808 V Capacitor: callback: 785170, pluginId: NativeBiometric, methodName: isAvailable, methodData: {}
[2025-06-04 15:57:55] 06-04 20:21:08.419  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785171, pluginId: Preferences, methodName: get
[2025-06-04 15:57:56] 06-04 20:21:08.420  7808  7808 V Capacitor: callback: 785171, pluginId: Preferences, methodName: get, methodData: {"key":"customer_preferences"}
[2025-06-04 15:57:56] 06-04 20:21:08.425  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785172, pluginId: OneSpanSecureStorage, methodName: getString
[2025-06-04 15:57:56] 06-04 20:21:08.425  7808  7808 V Capacitor: callback: 785172, pluginId: OneSpanSecureStorage, methodName: getString, methodData: {"forKey":"token"}
[2025-06-04 15:57:57] 06-04 20:21:08.427  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785173, pluginId: OneSpanSecureStorage, methodName: getString
[2025-06-04 15:57:57] 06-04 20:21:08.428  7808  7808 V Capacitor: callback: 785173, pluginId: OneSpanSecureStorage, methodName: getString, methodData: {"forKey":"token"}
[2025-06-04 15:57:57] 06-04 20:21:08.435  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785174, pluginId: DeviceManager, methodName: hasHuaweiServices
[2025-06-04 15:57:57] 06-04 20:21:08.436  7808  7808 V Capacitor: callback: 785174, pluginId: DeviceManager, methodName: hasHuaweiServices, methodData: {}
[2025-06-04 15:57:58] 06-04 20:21:08.443  7808  7808 I Capacitor/Console: File: http://localhost/2653.337a4456ae108e2e.js - Line 1 - Msg: ConfigCat - INFO - [5000] Evaluating 'aval-key'
[2025-06-04 15:57:58] 06-04 20:21:08.443  7808  7808 I Capacitor/Console:   Returning 'true'.
[2025-06-04 15:57:58] 06-04 20:21:08.446  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785175, pluginId: Preferences, methodName: get
[2025-06-04 15:57:59] 06-04 20:21:08.446  7808  7808 V Capacitor: callback: 785175, pluginId: Preferences, methodName: get, methodData: {"key":"customer_notifications"}
[2025-06-04 15:57:59] 06-04 20:21:08.452  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785176, pluginId: OneSpanSecureStorage, methodName: getString
[2025-06-04 15:57:59] 06-04 20:21:08.453  7808  7808 V Capacitor: callback: 785176, pluginId: OneSpanSecureStorage, methodName: getString, methodData: {"forKey":"tokenExp"}
[2025-06-04 15:58:00] 06-04 20:21:08.455  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785177, pluginId: OneSpanSecureStorage, methodName: getString
[2025-06-04 15:58:00] 06-04 20:21:08.456  7808  7808 V Capacitor: callback: 785177, pluginId: OneSpanSecureStorage, methodName: getString, methodData: {"forKey":"token"}
[2025-06-04 15:58:00] 06-04 20:21:08.562  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 114 - Msg: 🚀 MTH: subscribe [object Object]
[2025-06-04 15:58:00] 06-04 20:21:08.563  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 117 - Msg: 🚀 MTH: triggerMthSpaces isAudienceReady true
[2025-06-04 15:58:01] 06-04 20:21:08.565  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 117 - Msg: 🚀 MTH: triggerMthSpaces subscribers.length 1
[2025-06-04 15:58:01] 06-04 20:21:08.570  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 117 - Msg: 🚀 MTH: space [object Object]
[2025-06-04 15:58:01] 06-04 20:21:08.571  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 118 - Msg: 🚀 MTH: placement undefined
[2025-06-04 15:58:02] 06-04 20:21:08.573  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 118 - Msg: 🚀 MTH: return [object Object]
[2025-06-04 15:58:02] 06-04 20:21:08.574  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 117 - Msg: 🚀 MTH: prepareSpaceConfig [object Object]
[2025-06-04 15:58:02] 06-04 20:21:08.576  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 118 - Msg: 🚀 MTH: placement undefined
[2025-06-04 15:58:03] 06-04 20:21:08.577  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 118 - Msg: 🚀 MTH: return [object Object]
[2025-06-04 15:58:03] 06-04 20:21:08.578  7808  7808 E Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4188 - Msg: 🚀 mathildeSpaces placement [object Object]
[2025-06-04 15:58:03] 06-04 20:21:08.579  7808  7808 E Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4188 - Msg: 🚀 URL generada: https://endpoint2.mathilde-ads.com/?c=b&m=api&res=json&domain=www.bancodeoccidente.com.co&page=products&ua=Mozilla/5.0 (Linux; Android 13; sdk_gphone64_x86_64 Build/TE1A.240213.009; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/109.0.5414.123 Mobile Safari/537.36&dW=393&dH=778&secure=0&language=en-US&gdpr_consent=ALL&placementId=2822
[2025-06-04 15:58:03] 06-04 20:21:08.583  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785178, pluginId: DeviceManager, methodName: hasHuaweiServices
[2025-06-04 15:58:04] 06-04 20:21:08.590  7808  7808 V Capacitor: callback: 785178, pluginId: DeviceManager, methodName: hasHuaweiServices, methodData: {}
[2025-06-04 15:58:04] 06-04 20:21:08.592  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785179, pluginId: Preferences, methodName: set
[2025-06-04 15:58:04] 06-04 20:21:08.593  7808  7808 V Capacitor: callback: 785179, pluginId: Preferences, methodName: set, methodData: {"key":"customer_notifications","value":"[]"}
[2025-06-04 15:58:05] 06-04 20:21:08.594  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785180, pluginId: OneSpanSecureStorage, methodName: getString
[2025-06-04 15:58:05] 06-04 20:21:08.594  7808  7808 V Capacitor: callback: 785180, pluginId: OneSpanSecureStorage, methodName: getString, methodData: {"forKey":"token"}
[2025-06-04 15:58:05] 06-04 20:21:08.744  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 157 - Msg: Va a hacer el then del triggerview
[2025-06-04 15:58:05] 06-04 20:21:08.746  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 157 - Msg: then response :>>  [object Object]
[2025-06-04 15:58:06] 06-04 20:21:08.748  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.sync.js - Line 294 - Msg: at_property
[2025-06-04 15:58:06] 06-04 20:21:08.749  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: Triggerview TARGET - Finally View triggered on : /home/<USER>/products
[2025-06-04 15:58:06] 06-04 20:21:08.751  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.sync.js - Line 294 - Msg: at_property
[2025-06-04 15:58:07] 06-04 20:21:08.861  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.sync.js - Line 294 - Msg: at_property
[2025-06-04 15:58:07] 06-04 20:21:08.880  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.sync.js - Line 294 - Msg: at_property
[2025-06-04 15:58:07] 06-04 20:21:08.945  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785181, pluginId: App, methodName: addListener
[2025-06-04 15:58:07] 06-04 20:21:08.945  7808  7808 V Capacitor: callback: 785181, pluginId: App, methodName: addListener, methodData: {"eventName":"pause"}
[2025-06-04 15:58:08] 06-04 20:21:08.951  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785182, pluginId: App, methodName: addListener
[2025-06-04 15:58:08] 06-04 20:21:08.951  7808  7808 V Capacitor: callback: 785182, pluginId: App, methodName: addListener, methodData: {"eventName":"resume"}
[2025-06-04 15:58:08] 06-04 20:21:08.960  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785183, pluginId: PushNotifications, methodName: checkPermissions
[2025-06-04 15:58:09] 06-04 20:21:08.960  7808  7808 V Capacitor: callback: 785183, pluginId: PushNotifications, methodName: checkPermissions, methodData: {}
[2025-06-04 15:58:09] 06-04 20:21:08.961  7808  7808 I Capacitor/Console: File:  - Line 353 - Msg: undefined
[2025-06-04 15:58:09] 06-04 20:21:09.247  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785184, pluginId: OneSpanSecureStorage, methodName: getString
[2025-06-04 15:58:10] 06-04 20:21:09.247  7808  7808 V Capacitor: callback: 785184, pluginId: OneSpanSecureStorage, methodName: getString, methodData: {"forKey":"token"}
[2025-06-04 15:58:10] 06-04 20:21:09.289  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4209 - Msg: 🚀 Datos recibidos: [object Object]
[2025-06-04 15:58:10] 06-04 20:21:09.329  7808  7808 E Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4182 - Msg: Conatiner Result [object HTMLDivElement]
[2025-06-04 15:58:11] 06-04 20:21:09.374  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4184 - Msg: 🚀 Valores del evendata [object Object]
[2025-06-04 15:58:11] 06-04 20:21:09.374  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4184 - Msg: 🚀 Valores Del Element  [object HTMLAnchorElement]
[2025-06-04 15:58:11] 06-04 20:21:09.377  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4211 - Msg: 🚀 Finalmente ejecuto el Iframe
[2025-06-04 15:58:12] 06-04 20:21:13.361  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785185, pluginId: OneSpanSecureStorage, methodName: putString
[2025-06-04 15:58:12] 06-04 20:21:13.361  7808  7808 V Capacitor: callback: 785185, pluginId: OneSpanSecureStorage, methodName: putString, methodData: {"forKey":"tagsAval","forValue":"[{\"tagAval\":\"@OCJQU93746\",\"shortNumber\":\"0036\",\"type\":\"SDA\"},{\"tagAval\":\"@OCBMU93701Q\",\"shortNumber\":\"3312\",\"type\":\"SDA\"},{\"tagAval\":\"@OCASA937QA\",\"shortNumber\":\"3221\",\"type\":\"SDA\"},{\"tagAval\":\"@OCJHA099QA2\",\"shortNumber\":\"6514\",\"type\":\"DDA\"}]"}
[2025-06-04 15:58:12] 06-04 20:21:13.368  7808  7808 I Capacitor/Console: File: http://localhost/2653.337a4456ae108e2e.js - Line 1 - Msg: ConfigCat - INFO - [5000] Evaluating 'digital-wallet'
[2025-06-04 15:58:13] 06-04 20:21:13.368  7808  7808 I Capacitor/Console:   Returning 'true'.
[2025-06-04 15:58:13] 06-04 20:21:13.370  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785186, pluginId: OneSpanSecureStorage, methodName: getString
[2025-06-04 15:58:13] 06-04 20:21:13.370  7808  7808 V Capacitor: callback: 785186, pluginId: OneSpanSecureStorage, methodName: getString, methodData: {"forKey":"userWallet"}
[2025-06-04 15:58:14] 06-04 20:21:13.372  7808  7894 D Capacitor: Sending plugin error: {"save":false,"callbackId":"785186","pluginId":"OneSpanSecureStorage","methodName":"getString","success":false,"error":{"message":"Failed to get a string from Secure Storage, Storage does not contains requested key"}}
[2025-06-04 15:58:14] 06-04 20:21:13.460  7808  7859 D Capacitor: Handling local request: http://localhost/assets/shared/logos/breb.svg
[2025-06-04 15:58:14] 06-04 20:21:13.470  7808  7859 D Capacitor: Handling local request: http://localhost/assets/shared/logos/remittances-shadow.svg
[2025-06-04 15:58:14] 06-04 20:21:13.470  7808  7859 D Capacitor: Handling local request: http://localhost/assets/shared/logos/SOAT.svg
[2025-06-04 15:58:15] 06-04 20:21:13.473  7808  7875 D Capacitor: Handling local request: http://localhost/assets/shared/logos/banks/occidente.svg
[2025-06-04 15:58:15] 06-04 20:21:13.567  7808  7808 E Capacitor/Console: File: http://localhost/4650.99a220314a61f83a.js - Line 1 - Msg: ERROR TypeError: Cannot read properties of undefined (reading 'manager')
[2025-06-04 15:58:15] 06-04 20:21:13.569  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785187, pluginId: OneSpanSecureStorage, methodName: write
[2025-06-04 15:58:15] 06-04 20:21:13.570  7808  7808 V Capacitor: callback: 785187, pluginId: OneSpanSecureStorage, methodName: write, methodData: {}
[2025-06-04 15:58:16] 06-04 20:21:16.298  7808  7808 E Capacitor/Console: File: http://localhost/home/<USER>/products - Line 0 - Msg: Access to XMLHttpRequest at 'https://t2phemiav9.execute-api.us-east-2.amazonaws.com/stg/financial' from origin 'http://localhost' has been blocked by CORS policy: No 'Access-Control-Allow-Origin' header is present on the requested resource.
[2025-06-04 15:58:18] 06-04 20:22:14.106  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4255 - Msg: 1. Data------------> [object Object]
[2025-06-04 15:58:19] 06-04 20:22:14.125  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /home/<USER>/products/info
[2025-06-04 15:58:19] 06-04 20:22:14.126  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 551 - Msg: location changed carga de estilos => /home/<USER>/products/info
[2025-06-04 15:58:19] 06-04 20:22:14.126  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 907 - Msg: location changed carga de oferta => /home/<USER>/products/info
[2025-06-04 15:58:20] 06-04 20:22:14.127  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 2015 - Msg: location changed carga form1 => /home/<USER>/products/info
[2025-06-04 15:58:20] 06-04 20:22:14.128  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4165 - Msg: location changed carga form2 => /home/<USER>/products/info
[2025-06-04 15:58:20] 06-04 20:22:14.129  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /home/<USER>/products/info
[2025-06-04 15:58:21] 06-04 20:22:14.129  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /home/<USER>/products/info
[2025-06-04 15:58:21] 06-04 20:22:14.130  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /home/<USER>/products/info
[2025-06-04 15:58:21] 06-04 20:22:14.130  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /home/<USER>/products/info
[2025-06-04 15:58:22] 06-04 20:22:14.130  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /home/<USER>/products/info
[2025-06-04 15:58:22] 06-04 20:22:14.131  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /home/<USER>/products/info
[2025-06-04 15:58:22] 06-04 20:22:14.134  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /home/<USER>/products/info
[2025-06-04 15:58:22] 06-04 20:22:14.135  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /home/<USER>/products/info
[2025-06-04 15:58:23] 06-04 20:22:14.135  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /home/<USER>/products/info
[2025-06-04 15:58:23] 06-04 20:22:14.286  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.sync.js - Line 294 - Msg: at_property
[2025-06-04 15:58:23] 06-04 20:22:14.354  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785188, pluginId: DeviceManager, methodName: hasHuaweiServices
[2025-06-04 15:58:24] 06-04 20:22:14.354  7808  7808 V Capacitor: callback: 785188, pluginId: DeviceManager, methodName: hasHuaweiServices, methodData: {}
[2025-06-04 15:58:24] 06-04 20:22:14.355  7808  7808 I Capacitor/Console: File: http://localhost/2653.337a4456ae108e2e.js - Line 1 - Msg: ConfigCat - INFO - [5000] Evaluating 'customatizate-tag-aval'
[2025-06-04 15:58:24] 06-04 20:22:14.355  7808  7808 I Capacitor/Console:   Returning 'true'.
[2025-06-04 15:58:25] 06-04 20:22:14.373  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785189, pluginId: OneSpanSecureStorage, methodName: getString
[2025-06-04 15:58:25] 06-04 20:22:14.373  7808  7808 V Capacitor: callback: 785189, pluginId: OneSpanSecureStorage, methodName: getString, methodData: {"forKey":"token"}
[2025-06-04 15:58:25] 06-04 20:22:14.377  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785190, pluginId: OneSpanSecureStorage, methodName: getString
[2025-06-04 15:58:26] 06-04 20:22:14.377  7808  7808 V Capacitor: callback: 785190, pluginId: OneSpanSecureStorage, methodName: getString, methodData: {"forKey":"token"}
[2025-06-04 15:58:26] 06-04 20:22:14.380  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 108 - Msg: delete cookies document.location.pathname :>>  /home/<USER>/products/info
[2025-06-04 15:58:26] 06-04 20:22:14.380  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 111 - Msg: onUrlChange product_0CC or SecFormDataCard_ADL deleted
[2025-06-04 15:58:26] 06-04 20:22:14.390  7808  7859 D Capacitor: Handling local request: http://localhost/assets/shared/logos/document-error.svg
[2025-06-04 15:58:27] 06-04 20:22:14.391  7808  7857 D Capacitor: Handling local request: http://localhost/assets/shared/logos/currencies/cop-enabled.svg
[2025-06-04 15:58:27] 06-04 20:22:14.392  7808  7859 D Capacitor: Handling local request: http://localhost/assets/shared/logos/currencies/cop-disabled.svg
[2025-06-04 15:58:27] 06-04 20:22:14.396  7808  7859 D Capacitor: Handling local request: http://localhost/assets/shared/logos/tag-aval/tag-aval-line-original.png
[2025-06-04 15:58:28] 06-04 20:22:14.446  7808  7859 D Capacitor: Handling local request: http://localhost/assets/favicon.ico
[2025-06-04 15:58:28] 06-04 20:22:14.990  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.sync.js - Line 294 - Msg: at_property
[2025-06-04 15:58:28] 06-04 20:22:15.012  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.sync.js - Line 294 - Msg: at_property
[2025-06-04 15:58:29] 06-04 20:25:14.440  7808  7808 V Capacitor/AppPlugin: Notifying listeners for event pause
[2025-06-04 15:58:29] 06-04 20:25:14.453  7808  7808 D Capacitor: App paused
[2025-06-04 15:58:29] 06-04 20:25:14.477  7808  7808 I Capacitor/Console: File:  - Line 353 - Msg: undefined
[2025-06-04 15:58:30] 06-04 20:25:16.519  7808  7808 D Capacitor/AppPlugin: Firing change: false
[2025-06-04 15:58:30] 06-04 20:25:16.524  7808  7808 V Capacitor/AppPlugin: Notifying listeners for event appStateChange
[2025-06-04 15:58:30] 06-04 20:25:16.525  7808  7808 D Capacitor/AppPlugin: No listeners found for event appStateChange
[2025-06-04 15:58:31] 06-04 20:25:16.528  7808  7808 D Capacitor: App stopped
[2025-06-04 15:58:31] 06-04 20:25:16.531  7808  7808 D Capacitor: Saving instance state!
[2025-06-04 15:58:31] 06-04 20:25:21.457  7808  7808 D Capacitor: App restarted
[2025-06-04 15:58:32] 06-04 20:25:21.458  7808  7808 D Capacitor: App started
[2025-06-04 15:58:32] 06-04 20:25:21.460  7808  7808 D Capacitor/AppPlugin: Firing change: true
[2025-06-04 15:58:32] 06-04 20:25:21.460  7808  7808 V Capacitor/AppPlugin: Notifying listeners for event appStateChange
[2025-06-04 15:58:33] 06-04 20:25:21.460  7808  7808 D Capacitor/AppPlugin: No listeners found for event appStateChange
[2025-06-04 15:58:33] 06-04 20:25:21.461  7808  7808 V Capacitor/AppPlugin: Notifying listeners for event resume
[2025-06-04 15:58:33] 06-04 20:25:21.481  7808  7905 V Capacitor/NetworkPlugin: Notifying listeners for event networkStatusChange
[2025-06-04 15:58:33] 06-04 20:25:21.490  7808  7808 D Capacitor: App resumed
[2025-06-04 15:58:34] 06-04 20:25:21.493  7808  7808 I Capacitor/Console: File:  - Line 353 - Msg: undefined
[2025-06-04 15:58:34] 06-04 20:26:14.569  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/signout/inactivity
[2025-06-04 15:58:35] 06-04 20:26:14.569  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 551 - Msg: location changed carga de estilos => /authentication/signout/inactivity
[2025-06-04 15:58:35] 06-04 20:26:14.573  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 907 - Msg: location changed carga de oferta => /authentication/signout/inactivity
[2025-06-04 15:58:35] 06-04 20:26:14.574  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 2015 - Msg: location changed carga form1 => /authentication/signout/inactivity
[2025-06-04 15:58:36] 06-04 20:26:14.575  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4165 - Msg: location changed carga form2 => /authentication/signout/inactivity
[2025-06-04 15:58:36] 06-04 20:26:14.577  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/signout/inactivity
[2025-06-04 15:58:36] 06-04 20:26:14.577  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/signout/inactivity
[2025-06-04 15:58:37] 06-04 20:26:14.578  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/signout/inactivity
[2025-06-04 15:58:37] 06-04 20:26:14.580  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/signout/inactivity
[2025-06-04 15:58:37] 06-04 20:26:14.581  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/signout/inactivity
[2025-06-04 15:58:38] 06-04 20:26:14.586  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/signout/inactivity
[2025-06-04 15:58:38] 06-04 20:26:14.586  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/signout/inactivity
[2025-06-04 15:58:38] 06-04 20:26:14.587  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/signout/inactivity
[2025-06-04 15:58:39] 06-04 20:26:14.587  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/signout/inactivity
[2025-06-04 15:58:39] 06-04 20:26:14.588  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/signout/inactivity
[2025-06-04 15:58:39] 06-04 20:26:14.865  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785191, pluginId: App, methodName: removeListener
[2025-06-04 15:58:39] 06-04 20:26:14.865  7808  7808 V Capacitor: callback: 785191, pluginId: App, methodName: removeListener, methodData: {"eventName":"pause","callbackId":"785181"}
[2025-06-04 15:58:40] 06-04 20:26:14.867  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785192, pluginId: App, methodName: removeListener
[2025-06-04 15:58:40] 06-04 20:26:14.867  7808  7808 V Capacitor: callback: 785192, pluginId: App, methodName: removeListener, methodData: {"eventName":"resume","callbackId":"785182"}
[2025-06-04 15:58:40] 06-04 20:26:14.871  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.sync.js - Line 294 - Msg: at_property
[2025-06-04 15:58:41] 06-04 20:26:14.972  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785193, pluginId: OneSpanSecureStorage, methodName: getString
[2025-06-04 15:58:41] 06-04 20:26:14.972  7808  7808 V Capacitor: callback: 785193, pluginId: OneSpanSecureStorage, methodName: getString, methodData: {"forKey":"token"}
[2025-06-04 15:58:41] 06-04 20:26:14.974  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 108 - Msg: delete cookies document.location.pathname :>>  /authentication/signout/inactivity
[2025-06-04 15:58:42] 06-04 20:26:14.976  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 108 - Msg: delete cookies starting...
[2025-06-04 15:58:42] 06-04 20:26:14.980  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 106 - Msg: deleteCookies targetCampaingValue :>>  undefined
[2025-06-04 15:58:42] 06-04 20:26:14.986  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 108 - Msg: cookieName / storage deleted :>>  BB
[2025-06-04 15:58:43] 06-04 20:26:14.989  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 108 - Msg: cookieName / storage deleted :>>  PB_OCC_CC_DATA
[2025-06-04 15:58:43] 06-04 20:26:14.994  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 108 - Msg: cookieName / storage deleted :>>  PB_OCC_CC_DATAFORM
[2025-06-04 15:58:43] 06-04 20:26:14.994  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 108 - Msg: Las cookies (android) / localStorage (ios) han sido eliminadas : BB, PB_OCC_CC_DATA, PB_OCC_CC_DATAFORM
[2025-06-04 15:58:43] 06-04 20:26:14.995  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 111 - Msg: onUrlChange product_0CC or SecFormDataCard_ADL deleted
[2025-06-04 15:58:44] 06-04 20:26:15.000  7808  7857 D Capacitor: Handling local request: http://localhost/assets/authentication/logos/signout-session-inactivity.svg
[2025-06-04 15:58:44] 06-04 20:26:15.019  7808  7857 D Capacitor: Handling local request: http://localhost/assets/favicon.ico
[2025-06-04 15:58:44] 06-04 20:26:15.048  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785194, pluginId: OneSpanSecureStorage, methodName: getString
[2025-06-04 15:58:45] 06-04 20:26:15.051  7808  7808 V Capacitor: callback: 785194, pluginId: OneSpanSecureStorage, methodName: getString, methodData: {"forKey":"token"}
[2025-06-04 15:58:45] 06-04 20:26:15.559  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.sync.js - Line 294 - Msg: at_property
[2025-06-04 15:58:45] 06-04 20:26:15.576  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.sync.js - Line 294 - Msg: at_property
[2025-06-04 15:58:46] 06-04 20:26:15.643  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785195, pluginId: DigitalWallet, methodName: disconnect
[2025-06-04 15:58:46] 06-04 20:26:15.643  7808  7808 V Capacitor: callback: 785195, pluginId: DigitalWallet, methodName: disconnect, methodData: {}
[2025-06-04 15:58:46] 06-04 20:26:15.646  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785196, pluginId: OneSpanSecureStorage, methodName: contains
[2025-06-04 15:58:47] 06-04 20:26:15.647  7808  7808 V Capacitor: callback: 785196, pluginId: OneSpanSecureStorage, methodName: contains, methodData: {"forKey":"token"}
[2025-06-04 15:58:47] 06-04 20:26:15.649  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785197, pluginId: OneSpanSecureStorage, methodName: contains
[2025-06-04 15:58:47] 06-04 20:26:15.650  7808  7808 V Capacitor: callback: 785197, pluginId: OneSpanSecureStorage, methodName: contains, methodData: {"forKey":"tokenExp"}
[2025-06-04 15:58:48] 06-04 20:26:15.704  7808  7808 E Capacitor/Console: File: http://localhost/4650.99a220314a61f83a.js - Line 1 - Msg: ERROR TypeError: Cannot read properties of undefined (reading 'manager')
[2025-06-04 15:58:48] 06-04 20:26:15.705  7808  7808 E Capacitor/Console: File: http://localhost/4650.99a220314a61f83a.js - Line 1 - Msg: ERROR TypeError: Cannot read properties of undefined (reading 'manager')
[2025-06-04 15:58:48] 06-04 20:26:15.706  7808  7808 E Capacitor/Console: File: http://localhost/4650.99a220314a61f83a.js - Line 1 - Msg: ERROR TypeError: Cannot read properties of undefined (reading 'manager')
[2025-06-04 15:58:49] 06-04 20:26:15.710  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785198, pluginId: OneSpanSecureStorage, methodName: remove
[2025-06-04 15:58:49] 06-04 20:26:15.710  7808  7808 V Capacitor: callback: 785198, pluginId: OneSpanSecureStorage, methodName: remove, methodData: {"forKey":"token"}
[2025-06-04 15:58:49] 06-04 20:26:15.712  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785199, pluginId: OneSpanSecureStorage, methodName: remove
[2025-06-04 15:58:49] 06-04 20:26:15.712  7808  7808 V Capacitor: callback: 785199, pluginId: OneSpanSecureStorage, methodName: remove, methodData: {"forKey":"tokenExp"}
[2025-06-04 15:58:50] 06-04 20:26:15.722  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785200, pluginId: OneSpanSecureStorage, methodName: write
[2025-06-04 15:58:50] 06-04 20:26:15.722  7808  7808 V Capacitor: callback: 785200, pluginId: OneSpanSecureStorage, methodName: write, methodData: {}
[2025-06-04 15:58:50] 06-04 20:26:15.734  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785201, pluginId: OneSpanSecureStorage, methodName: write
[2025-06-04 15:58:51] 06-04 20:26:15.737  7808  7808 V Capacitor: callback: 785201, pluginId: OneSpanSecureStorage, methodName: write, methodData: {}
[2025-06-04 15:58:51] 06-04 20:26:15.980  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785202, pluginId: Preferences, methodName: remove
[2025-06-04 15:58:51] 06-04 20:26:15.980  7808  7808 V Capacitor: callback: 785202, pluginId: Preferences, methodName: remove, methodData: {"key":"customer_session"}
[2025-06-04 15:58:52] 06-04 20:26:15.991  7808  7808 I Capacitor/Console: File:  - Line 353 - Msg: undefined
[2025-06-04 15:58:52] 06-04 20:29:23.757  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4255 - Msg: 1. Data------------> [object Object]
[2025-06-04 15:58:52] 06-04 20:29:23.758  7808  7808 W Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4215 - Msg: 2. send to: -------------> [object Object]
[2025-06-04 15:58:53] 06-04 20:29:24.026  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/login
[2025-06-04 15:58:53] 06-04 20:29:24.028  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 551 - Msg: location changed carga de estilos => /authentication/login
[2025-06-04 15:58:53] 06-04 20:29:24.030  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 907 - Msg: location changed carga de oferta => /authentication/login
[2025-06-04 15:58:53] 06-04 20:29:24.038  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 2015 - Msg: location changed carga form1 => /authentication/login
[2025-06-04 15:58:54] 06-04 20:29:24.039  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4165 - Msg: location changed carga form2 => /authentication/login
[2025-06-04 15:58:54] 06-04 20:29:24.041  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/login
[2025-06-04 15:58:54] 06-04 20:29:24.042  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/login
[2025-06-04 15:58:55] 06-04 20:29:24.044  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/login
[2025-06-04 15:58:55] 06-04 20:29:24.046  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/login
[2025-06-04 15:58:55] 06-04 20:29:24.047  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/login
[2025-06-04 15:58:56] 06-04 20:29:24.048  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/login
[2025-06-04 15:58:56] 06-04 20:29:24.048  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/login
[2025-06-04 15:58:56] 06-04 20:29:24.048  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/login
[2025-06-04 15:58:56] 06-04 20:29:24.049  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/login
[2025-06-04 15:58:57] 06-04 20:29:24.050  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/login
[2025-06-04 15:58:57] 06-04 20:29:24.050  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/login
[2025-06-04 15:58:57] 06-04 20:29:24.050  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/login
[2025-06-04 15:58:58] 06-04 20:29:24.230  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.sync.js - Line 294 - Msg: at_property
[2025-06-04 15:58:58] 06-04 20:29:24.366  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785203, pluginId: Preferences, methodName: get
[2025-06-04 15:58:58] 06-04 20:29:24.366  7808  7808 V Capacitor: callback: 785203, pluginId: Preferences, methodName: get, methodData: {"key":"customer"}
[2025-06-04 15:58:59] 06-04 20:29:24.367  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785204, pluginId: NativeBiometric, methodName: isAvailable
[2025-06-04 15:58:59] 06-04 20:29:24.367  7808  7808 V Capacitor: callback: 785204, pluginId: NativeBiometric, methodName: isAvailable, methodData: {}
[2025-06-04 15:58:59] 06-04 20:29:24.370  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785205, pluginId: Preferences, methodName: get
[2025-06-04 15:59:00] 06-04 20:29:24.371  7808  7808 V Capacitor: callback: 785205, pluginId: Preferences, methodName: get, methodData: {"key":"biometric_linked"}
[2025-06-04 15:59:00] 06-04 20:29:24.379  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785206, pluginId: OneSpanSecureStorage, methodName: getString
[2025-06-04 15:59:00] 06-04 20:29:24.380  7808  7808 V Capacitor: callback: 785206, pluginId: OneSpanSecureStorage, methodName: getString, methodData: {"forKey":"token"}
[2025-06-04 15:59:00] 06-04 20:29:24.383  7808  7894 D Capacitor: Sending plugin error: {"save":false,"callbackId":"785206","pluginId":"OneSpanSecureStorage","methodName":"getString","success":false,"error":{"message":"Failed to get a string from Secure Storage, Storage does not contains requested key"}}
[2025-06-04 15:59:01] 06-04 20:29:24.384  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785207, pluginId: OneSpanSecureStorage, methodName: getString
[2025-06-04 15:59:01] 06-04 20:29:24.385  7808  7808 V Capacitor: callback: 785207, pluginId: OneSpanSecureStorage, methodName: getString, methodData: {"forKey":"tagsAval"}
[2025-06-04 15:59:01] 06-04 20:29:24.387  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785208, pluginId: OneSpanSecureStorage, methodName: getString
[2025-06-04 15:59:02] 06-04 20:29:24.387  7808  7808 V Capacitor: callback: 785208, pluginId: OneSpanSecureStorage, methodName: getString, methodData: {"forKey":"tagAval"}
[2025-06-04 15:59:02] 06-04 20:29:24.389  7808  7894 D Capacitor: Sending plugin error: {"save":false,"callbackId":"785208","pluginId":"OneSpanSecureStorage","methodName":"getString","success":false,"error":{"message":"Failed to get a string from Secure Storage, Storage does not contains requested key"}}
[2025-06-04 15:59:02] 06-04 20:29:24.449  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 108 - Msg: delete cookies document.location.pathname :>>  /authentication/login
[2025-06-04 15:59:03] 06-04 20:29:24.450  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 108 - Msg: delete cookies starting...
[2025-06-04 15:59:03] 06-04 20:29:24.452  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 106 - Msg: deleteCookies targetCampaingValue :>>  undefined
[2025-06-04 15:59:03] 06-04 20:29:24.457  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 108 - Msg: cookieName / storage deleted :>>  BB
[2025-06-04 15:59:03] 06-04 20:29:24.461  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 108 - Msg: cookieName / storage deleted :>>  PB_OCC_CC_DATA
[2025-06-04 15:59:04] 06-04 20:29:24.465  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 108 - Msg: cookieName / storage deleted :>>  PB_OCC_CC_DATAFORM
[2025-06-04 15:59:04] 06-04 20:29:24.466  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 108 - Msg: Las cookies (android) / localStorage (ios) han sido eliminadas : BB, PB_OCC_CC_DATA, PB_OCC_CC_DATAFORM
[2025-06-04 15:59:04] 06-04 20:29:24.466  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 111 - Msg: onUrlChange product_0CC or SecFormDataCard_ADL deleted
[2025-06-04 15:59:05] 06-04 20:29:24.491  7808  7857 D Capacitor: Handling local request: http://localhost/assets/favicon.ico
[2025-06-04 15:59:05] 06-04 20:29:24.801  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.sync.js - Line 294 - Msg: at_property
[2025-06-04 15:59:05] 06-04 20:29:24.830  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.sync.js - Line 294 - Msg: at_property
[2025-06-04 15:59:05] 06-04 20:29:24.917  7808  7808 I Capacitor/Console: File: http://localhost/2653.337a4456ae108e2e.js - Line 1 - Msg: ConfigCat - INFO - [5000] Evaluating 'visible-tag-aval-login'
[2025-06-04 15:59:06] 06-04 20:29:24.917  7808  7808 I Capacitor/Console:   Returning 'true'.
[2025-06-04 15:59:06] 06-04 20:29:30.217  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4255 - Msg: 1. Data------------> [object Object]
[2025-06-04 15:59:06] 06-04 20:29:30.502  7808  7857 D Capacitor: Handling local request: http://localhost/assets/favicon.ico
[2025-06-04 15:59:07] 06-04 20:29:31.890  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4255 - Msg: 1. Data------------> [object Object]
[2025-06-04 15:59:07] 06-04 20:29:33.051  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4255 - Msg: 1. Data------------> [object Object]
[2025-06-04 15:59:07] 06-04 20:29:33.051  7808  7808 W Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4215 - Msg: 2. send to: -------------> [object Object]
[2025-06-04 15:59:08] 06-04 20:29:33.108  7808  7808 I Capacitor/Console: File: http://localhost/2653.337a4456ae108e2e.js - Line 1 - Msg: ConfigCat - INFO - [5000] Evaluating 'new-login'
[2025-06-04 15:59:08] 06-04 20:29:33.108  7808  7808 I Capacitor/Console:   Returning 'false'.
[2025-06-04 15:59:08] 06-04 20:29:33.183  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 78 - Msg: generateUserID typeDoc :>>  cc 
[2025-06-04 15:59:09] 06-04 20:29:33.183  7808  7808 I Capacitor/Console: numDoc :>>  91539937
[2025-06-04 15:59:09] 06-04 20:29:33.184  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 151 - Msg: Login Button clicked! addListenerBBCookie Cookie BB with adlID :>>  37/55698687234390BB 
[2025-06-04 15:59:09] 06-04 20:29:33.184  7808  7808 I Capacitor/Console: type_doc:  CC num_doc 91539937
[2025-06-04 15:59:09] 06-04 20:29:33.187  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 126 - Msg: 🚀 MTH: Audiences API init
[2025-06-04 15:59:10] 06-04 20:29:33.309  7808  7857 D Capacitor: Handling local request: http://localhost/assets/favicon.ico
[2025-06-04 15:59:10] 06-04 20:29:33.796  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 131 - Msg: 🚀 MTH: Audiences API success
[2025-06-04 15:59:10] 06-04 20:29:33.838  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 131 - Msg: 🚀 MTH: data [object Object]
[2025-06-04 15:59:11] 06-04 20:29:33.840  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 116 - Msg: 🚀 MTH: setAudience undefined
[2025-06-04 15:59:11] 06-04 20:29:35.726  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785209, pluginId: OneSpanSecureStorage, methodName: getString
[2025-06-04 15:59:11] 06-04 20:29:35.726  7808  7808 V Capacitor: callback: 785209, pluginId: OneSpanSecureStorage, methodName: getString, methodData: {"forKey":"token"}
[2025-06-04 15:59:12] 06-04 20:29:35.727  7808  7894 D Capacitor: Sending plugin error: {"save":false,"callbackId":"785209","pluginId":"OneSpanSecureStorage","methodName":"getString","success":false,"error":{"message":"Failed to get a string from Secure Storage, Storage does not contains requested key"}}
[2025-06-04 15:59:12] 06-04 20:29:35.728  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785210, pluginId: OneSpanSecureStorage, methodName: putString
[2025-06-04 15:59:12] 06-04 20:29:35.729  7808  7808 V Capacitor: callback: 785210, pluginId: OneSpanSecureStorage, methodName: putString, methodData: {"forKey":"tmpPassword","forValue":"12312314"}
[2025-06-04 15:59:13] 06-04 20:29:35.745  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785211, pluginId: OneSpanSecureStorage, methodName: write
[2025-06-04 15:59:13] 06-04 20:29:35.746  7808  7808 V Capacitor: callback: 785211, pluginId: OneSpanSecureStorage, methodName: write, methodData: {}
[2025-06-04 15:59:13] 06-04 20:29:35.932  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785212, pluginId: OneSpanSecureStorage, methodName: putString
[2025-06-04 15:59:13] 06-04 20:29:35.932  7808  7808 V Capacitor: callback: 785212, pluginId: OneSpanSecureStorage, methodName: putString, methodData: {"forKey":"biometricUsertype","forValue":"CC"}
[2025-06-04 15:59:14] 06-04 20:29:35.933  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785213, pluginId: OneSpanSecureStorage, methodName: putString
[2025-06-04 15:59:14] 06-04 20:29:35.934  7808  7808 V Capacitor: callback: 785213, pluginId: OneSpanSecureStorage, methodName: putString, methodData: {"forKey":"biometricUsername","forValue":"91539937"}
[2025-06-04 15:59:14] 06-04 20:29:35.935  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785214, pluginId: OneSpanSecureStorage, methodName: putString
[2025-06-04 15:59:15] 06-04 20:29:35.936  7808  7808 V Capacitor: callback: 785214, pluginId: OneSpanSecureStorage, methodName: putString, methodData: {"forKey":"biometricPassword","forValue":"12312314"}
[2025-06-04 15:59:15] 06-04 20:29:35.955  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785215, pluginId: OneSpanSecureStorage, methodName: write
[2025-06-04 15:59:15] 06-04 20:29:35.956  7808  7808 V Capacitor: callback: 785215, pluginId: OneSpanSecureStorage, methodName: write, methodData: {}
[2025-06-04 15:59:16] 06-04 20:29:35.958  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785216, pluginId: OneSpanSecureStorage, methodName: write
[2025-06-04 15:59:16] 06-04 20:29:35.959  7808  7808 V Capacitor: callback: 785216, pluginId: OneSpanSecureStorage, methodName: write, methodData: {}
[2025-06-04 15:59:16] 06-04 20:29:35.960  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785217, pluginId: OneSpanSecureStorage, methodName: write
[2025-06-04 15:59:17] 06-04 20:29:35.961  7808  7808 V Capacitor: callback: 785217, pluginId: OneSpanSecureStorage, methodName: write, methodData: {}
[2025-06-04 15:59:17] 06-04 20:29:36.045  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785218, pluginId: DigitalWallet, methodName: disconnect
[2025-06-04 15:59:17] 06-04 20:29:36.045  7808  7808 V Capacitor: callback: 785218, pluginId: DigitalWallet, methodName: disconnect, methodData: {}
[2025-06-04 15:59:17] 06-04 20:29:36.049  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785219, pluginId: OneSpanSecureStorage, methodName: contains
[2025-06-04 15:59:18] 06-04 20:29:36.049  7808  7808 V Capacitor: callback: 785219, pluginId: OneSpanSecureStorage, methodName: contains, methodData: {"forKey":"token"}
[2025-06-04 15:59:18] 06-04 20:29:36.054  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785220, pluginId: OneSpanSecureStorage, methodName: contains
[2025-06-04 15:59:18] 06-04 20:29:36.054  7808  7808 V Capacitor: callback: 785220, pluginId: OneSpanSecureStorage, methodName: contains, methodData: {"forKey":"tokenExp"}
[2025-06-04 15:59:19] 06-04 20:29:36.060  7808  7808 E Capacitor/Console: File: http://localhost/4650.99a220314a61f83a.js - Line 1 - Msg: ERROR TypeError: Cannot read properties of undefined (reading 'manager')
[2025-06-04 15:59:19] 06-04 20:29:36.062  7808  7808 E Capacitor/Console: File: http://localhost/4650.99a220314a61f83a.js - Line 1 - Msg: ERROR TypeError: Cannot read properties of undefined (reading 'manager')
[2025-06-04 15:59:19] 06-04 20:29:36.495  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/errors/service-failure
[2025-06-04 15:59:19] 06-04 20:29:36.495  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 551 - Msg: location changed carga de estilos => /authentication/errors/service-failure
[2025-06-04 15:59:20] 06-04 20:29:36.495  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 907 - Msg: location changed carga de oferta => /authentication/errors/service-failure
[2025-06-04 15:59:20] 06-04 20:29:36.495  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 2015 - Msg: location changed carga form1 => /authentication/errors/service-failure
[2025-06-04 15:59:20] 06-04 20:29:36.501  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4165 - Msg: location changed carga form2 => /authentication/errors/service-failure
[2025-06-04 15:59:20] 06-04 20:29:36.502  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/errors/service-failure
[2025-06-04 15:59:21] 06-04 20:29:36.502  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/errors/service-failure
[2025-06-04 15:59:21] 06-04 20:29:36.503  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/errors/service-failure
[2025-06-04 15:59:21] 06-04 20:29:36.504  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/errors/service-failure
[2025-06-04 15:59:22] 06-04 20:29:36.506  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/errors/service-failure
[2025-06-04 15:59:22] 06-04 20:29:36.507  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/errors/service-failure
[2025-06-04 15:59:22] 06-04 20:29:36.507  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/errors/service-failure
[2025-06-04 15:59:22] 06-04 20:29:36.508  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/errors/service-failure
[2025-06-04 15:59:23] 06-04 20:29:36.509  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/errors/service-failure
[2025-06-04 15:59:23] 06-04 20:29:36.510  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/errors/service-failure
[2025-06-04 15:59:23] 06-04 20:29:36.510  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/errors/service-failure
[2025-06-04 15:59:24] 06-04 20:29:36.511  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/errors/service-failure
[2025-06-04 15:59:24] 06-04 20:29:36.511  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/errors/service-failure
[2025-06-04 15:59:24] 06-04 20:29:36.512  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/errors/service-failure
[2025-06-04 15:59:24] 06-04 20:29:36.512  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/errors/service-failure
[2025-06-04 15:59:25] 06-04 20:29:36.512  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/errors/service-failure
[2025-06-04 15:59:25] 06-04 20:29:36.514  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/errors/service-failure
[2025-06-04 15:59:25] 06-04 20:29:36.635  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.sync.js - Line 294 - Msg: at_property
[2025-06-04 15:59:26] 06-04 20:29:36.677  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 108 - Msg: delete cookies document.location.pathname :>>  /authentication/errors/service-failure
[2025-06-04 15:59:26] 06-04 20:29:36.677  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 111 - Msg: onUrlChange product_0CC or SecFormDataCard_ADL deleted
[2025-06-04 15:59:26] 06-04 20:29:36.684  7808  7857 D Capacitor: Handling local request: http://localhost/assets/authentication/logos/error-service-failure.svg
[2025-06-04 15:59:26] 06-04 20:29:36.701  7808  7857 D Capacitor: Handling local request: http://localhost/assets/favicon.ico
[2025-06-04 15:59:27] 06-04 20:29:36.702  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785221, pluginId: Preferences, methodName: remove
[2025-06-04 15:59:27] 06-04 20:29:36.703  7808  7808 V Capacitor: callback: 785221, pluginId: Preferences, methodName: remove, methodData: {"key":"customer_session"}
[2025-06-04 15:59:27] 06-04 20:29:36.733  7808  7808 I Capacitor/Console: File:  - Line 353 - Msg: undefined
[2025-06-04 15:59:28] 06-04 20:29:36.926  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.sync.js - Line 294 - Msg: at_property
[2025-06-04 15:59:28] 06-04 20:29:36.946  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.sync.js - Line 294 - Msg: at_property
[2025-06-04 15:59:28] 06-04 20:29:39.362  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4255 - Msg: 1. Data------------> [object Object]
[2025-06-04 15:59:28] 06-04 20:29:39.363  7808  7808 W Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4215 - Msg: 2. send to: -------------> [object Object]
[2025-06-04 15:59:29] 06-04 20:29:39.415  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/login
[2025-06-04 15:59:29] 06-04 20:29:39.415  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 551 - Msg: location changed carga de estilos => /authentication/login
[2025-06-04 15:59:29] 06-04 20:29:39.416  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 907 - Msg: location changed carga de oferta => /authentication/login
[2025-06-04 15:59:30] 06-04 20:29:39.417  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 2015 - Msg: location changed carga form1 => /authentication/login
[2025-06-04 15:59:30] 06-04 20:29:39.418  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4165 - Msg: location changed carga form2 => /authentication/login
[2025-06-04 15:59:30] 06-04 20:29:39.419  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/login
[2025-06-04 15:59:30] 06-04 20:29:39.420  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/login
[2025-06-04 15:59:31] 06-04 20:29:39.421  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/login
[2025-06-04 15:59:31] 06-04 20:29:39.422  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/login
[2025-06-04 15:59:31] 06-04 20:29:39.423  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/login
[2025-06-04 15:59:32] 06-04 20:29:39.424  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/login
[2025-06-04 15:59:32] 06-04 20:29:39.425  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/login
[2025-06-04 15:59:32] 06-04 20:29:39.425  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/login
[2025-06-04 15:59:32] 06-04 20:29:39.426  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/login
[2025-06-04 15:59:33] 06-04 20:29:39.427  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/login
[2025-06-04 15:59:33] 06-04 20:29:39.427  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/login
[2025-06-04 15:59:33] 06-04 20:29:39.428  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/login
[2025-06-04 15:59:34] 06-04 20:29:39.429  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/login
[2025-06-04 15:59:34] 06-04 20:29:39.429  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/login
[2025-06-04 15:59:34] 06-04 20:29:39.430  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/login
[2025-06-04 15:59:35] 06-04 20:29:39.431  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/login
[2025-06-04 15:59:35] 06-04 20:29:39.432  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/login
[2025-06-04 15:59:35] 06-04 20:29:39.433  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/login
[2025-06-04 15:59:35] 06-04 20:29:39.434  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /authentication/login
[2025-06-04 15:59:36] 06-04 20:29:39.525  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.sync.js - Line 294 - Msg: at_property
[2025-06-04 15:59:36] 06-04 20:29:39.581  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785222, pluginId: Preferences, methodName: get
[2025-06-04 15:59:36] 06-04 20:29:39.581  7808  7808 V Capacitor: callback: 785222, pluginId: Preferences, methodName: get, methodData: {"key":"customer"}
[2025-06-04 15:59:37] 06-04 20:29:39.583  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785223, pluginId: NativeBiometric, methodName: isAvailable
[2025-06-04 15:59:37] 06-04 20:29:39.583  7808  7808 V Capacitor: callback: 785223, pluginId: NativeBiometric, methodName: isAvailable, methodData: {}
[2025-06-04 15:59:37] 06-04 20:29:39.585  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785224, pluginId: Preferences, methodName: get
[2025-06-04 15:59:37] 06-04 20:29:39.586  7808  7808 V Capacitor: callback: 785224, pluginId: Preferences, methodName: get, methodData: {"key":"biometric_linked"}
[2025-06-04 15:59:38] 06-04 20:29:39.587  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785225, pluginId: OneSpanSecureStorage, methodName: getString
[2025-06-04 15:59:38] 06-04 20:29:39.587  7808  7808 V Capacitor: callback: 785225, pluginId: OneSpanSecureStorage, methodName: getString, methodData: {"forKey":"token"}
[2025-06-04 15:59:38] 06-04 20:29:39.588  7808  7894 D Capacitor: Sending plugin error: {"save":false,"callbackId":"785225","pluginId":"OneSpanSecureStorage","methodName":"getString","success":false,"error":{"message":"Failed to get a string from Secure Storage, Storage does not contains requested key"}}
[2025-06-04 15:59:39] 06-04 20:29:39.589  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785226, pluginId: OneSpanSecureStorage, methodName: getString
[2025-06-04 15:59:39] 06-04 20:29:39.589  7808  7808 V Capacitor: callback: 785226, pluginId: OneSpanSecureStorage, methodName: getString, methodData: {"forKey":"tagsAval"}
[2025-06-04 15:59:39] 06-04 20:29:39.591  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785227, pluginId: OneSpanSecureStorage, methodName: getString
[2025-06-04 15:59:40] 06-04 20:29:39.592  7808  7808 V Capacitor: callback: 785227, pluginId: OneSpanSecureStorage, methodName: getString, methodData: {"forKey":"tagAval"}
[2025-06-04 15:59:40] 06-04 20:29:39.592  7808  7808 I Capacitor/Console: File: http://localhost/2653.337a4456ae108e2e.js - Line 1 - Msg: ConfigCat - INFO - [5000] Evaluating 'visible-tag-aval-login'
[2025-06-04 15:59:40] 06-04 20:29:39.592  7808  7808 I Capacitor/Console:   Returning 'true'.
[2025-06-04 15:59:40] 06-04 20:29:39.593  7808  7894 D Capacitor: Sending plugin error: {"save":false,"callbackId":"785227","pluginId":"OneSpanSecureStorage","methodName":"getString","success":false,"error":{"message":"Failed to get a string from Secure Storage, Storage does not contains requested key"}}
[2025-06-04 15:59:41] 06-04 20:29:39.593  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 108 - Msg: delete cookies document.location.pathname :>>  /authentication/login
[2025-06-04 15:59:41] 06-04 20:29:39.595  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 108 - Msg: delete cookies starting...
[2025-06-04 15:59:41] 06-04 20:29:39.596  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 106 - Msg: deleteCookies targetCampaingValue :>>  undefined
[2025-06-04 15:59:42] 06-04 20:29:39.601  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 108 - Msg: cookieName / storage deleted :>>  BB
[2025-06-04 15:59:42] 06-04 20:29:39.606  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 108 - Msg: cookieName / storage deleted :>>  PB_OCC_CC_DATA
[2025-06-04 15:59:42] 06-04 20:29:39.609  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 108 - Msg: cookieName / storage deleted :>>  PB_OCC_CC_DATAFORM
[2025-06-04 15:59:43] 06-04 20:29:39.609  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 108 - Msg: Las cookies (android) / localStorage (ios) han sido eliminadas : BB, PB_OCC_CC_DATA, PB_OCC_CC_DATAFORM
[2025-06-04 15:59:43] 06-04 20:29:39.610  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 111 - Msg: onUrlChange product_0CC or SecFormDataCard_ADL deleted
[2025-06-04 15:59:43] 06-04 20:29:39.644  7808  7857 D Capacitor: Handling local request: http://localhost/assets/favicon.ico
[2025-06-04 15:59:43] 06-04 20:29:39.790  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.sync.js - Line 294 - Msg: at_property
[2025-06-04 15:59:44] 06-04 20:29:39.804  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.sync.js - Line 294 - Msg: at_property
[2025-06-04 15:59:44] 06-04 20:29:40.991  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4255 - Msg: 1. Data------------> [object Object]
[2025-06-04 15:59:44] 06-04 20:29:41.192  7808  7857 D Capacitor: Handling local request: http://localhost/assets/favicon.ico
[2025-06-04 15:59:45] 06-04 20:29:42.536  7808  7857 D Capacitor: Handling local request: http://localhost/assets/favicon.ico
[2025-06-04 15:59:45] 06-04 20:29:43.388  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4255 - Msg: 1. Data------------> [object Object]
[2025-06-04 15:59:46] 06-04 20:29:43.561  7808  7857 D Capacitor: Handling local request: http://localhost/assets/favicon.ico
[2025-06-04 15:59:46] 06-04 20:29:49.308  7808  7857 D Capacitor: Handling local request: http://localhost/assets/favicon.ico
[2025-06-04 15:59:46] 06-04 20:29:51.068  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4255 - Msg: 1. Data------------> [object Object]
[2025-06-04 15:59:47] 06-04 20:29:52.304  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4255 - Msg: 1. Data------------> [object Object]
[2025-06-04 15:59:47] 06-04 20:29:52.304  7808  7808 W Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4215 - Msg: 2. send to: -------------> [object Object]
[2025-06-04 15:59:47] 06-04 20:29:52.352  7808  7808 I Capacitor/Console: File: http://localhost/2653.337a4456ae108e2e.js - Line 1 - Msg: ConfigCat - INFO - [5000] Evaluating 'new-login'
[2025-06-04 15:59:48] 06-04 20:29:52.352  7808  7808 I Capacitor/Console:   Returning 'false'.
[2025-06-04 15:59:48] 06-04 20:29:52.410  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 78 - Msg: generateUserID typeDoc :>>  cc 
[2025-06-04 15:59:48] 06-04 20:29:52.410  7808  7808 I Capacitor/Console: numDoc :>>  91539937
[2025-06-04 15:59:49] 06-04 20:29:52.411  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 151 - Msg: Login Button clicked! addListenerBBCookie Cookie BB with adlID :>>  37/55698687234390BB 
[2025-06-04 15:59:49] 06-04 20:29:52.411  7808  7808 I Capacitor/Console: type_doc:  CC num_doc 91539937
[2025-06-04 15:59:49] 06-04 20:29:52.413  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 126 - Msg: 🚀 MTH: Audiences API init
[2025-06-04 15:59:49] 06-04 20:29:52.542  7808  7857 D Capacitor: Handling local request: http://localhost/assets/favicon.ico
[2025-06-04 15:59:50] 06-04 20:29:52.896  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 131 - Msg: 🚀 MTH: Audiences API success
[2025-06-04 15:59:50] 06-04 20:29:52.935  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 131 - Msg: 🚀 MTH: data [object Object]
[2025-06-04 15:59:50] 06-04 20:29:52.936  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 116 - Msg: 🚀 MTH: setAudience undefined
[2025-06-04 15:59:51] 06-04 20:29:56.753  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785228, pluginId: OneSpanSecureStorage, methodName: putString
[2025-06-04 15:59:51] 06-04 20:29:56.754  7808  7808 V Capacitor: callback: 785228, pluginId: OneSpanSecureStorage, methodName: putString, methodData: {"forKey":"token","forValue":"**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}
[2025-06-04 15:59:51] 06-04 20:29:56.786  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785229, pluginId: OneSpanSecureStorage, methodName: getString
[2025-06-04 15:59:52] 06-04 20:29:56.787  7808  7808 V Capacitor: callback: 785229, pluginId: OneSpanSecureStorage, methodName: getString, methodData: {"forKey":"token"}
[2025-06-04 15:59:52] 06-04 20:29:56.792  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785230, pluginId: OneSpanSecureStorage, methodName: write
[2025-06-04 15:59:52] 06-04 20:29:56.792  7808  7808 V Capacitor: callback: 785230, pluginId: OneSpanSecureStorage, methodName: write, methodData: {}
[2025-06-04 15:59:53] 06-04 20:29:56.961  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785231, pluginId: OneSpanSecureStorage, methodName: putString
[2025-06-04 15:59:53] 06-04 20:29:56.961  7808  7808 V Capacitor: callback: 785231, pluginId: OneSpanSecureStorage, methodName: putString, methodData: {"forKey":"tokenExp","forValue":"1749072596000"}
[2025-06-04 15:59:53] 06-04 20:29:56.972  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785232, pluginId: OneSpanSecureStorage, methodName: write
[2025-06-04 15:59:53] 06-04 20:29:56.972  7808  7808 V Capacitor: callback: 785232, pluginId: OneSpanSecureStorage, methodName: write, methodData: {}
[2025-06-04 15:59:54] 06-04 20:30:06.089  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785233, pluginId: Preferences, methodName: get
[2025-06-04 15:59:54] 06-04 20:30:06.090  7808  7808 V Capacitor: callback: 785233, pluginId: Preferences, methodName: get, methodData: {"key":"customer_silent_migration"}
[2025-06-04 15:59:55] 06-04 20:30:06.118  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785234, pluginId: OneSpanSecureStorage, methodName: putString
[2025-06-04 15:59:55] 06-04 20:30:06.118  7808  7808 V Capacitor: callback: 785234, pluginId: OneSpanSecureStorage, methodName: putString, methodData: {"forKey":"tmpPassword","forValue":"12312312314"}
[2025-06-04 15:59:55] 06-04 20:30:06.128  7808  7808 E Capacitor/Console: File: http://localhost/4650.99a220314a61f83a.js - Line 1 - Msg: ERROR TypeError: Cannot read properties of undefined (reading 'manager')
[2025-06-04 15:59:56] 06-04 20:30:06.135  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785235, pluginId: OneSpanSecureStorage, methodName: write
[2025-06-04 15:59:56] 06-04 20:30:06.135  7808  7808 V Capacitor: callback: 785235, pluginId: OneSpanSecureStorage, methodName: write, methodData: {}
[2025-06-04 15:59:56] 06-04 20:30:06.331  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785236, pluginId: OneSpanSecureStorage, methodName: putString
[2025-06-04 15:59:57] 06-04 20:30:06.331  7808  7808 V Capacitor: callback: 785236, pluginId: OneSpanSecureStorage, methodName: putString, methodData: {"forKey":"biometricUsertype","forValue":"CC"}
[2025-06-04 15:59:57] 06-04 20:30:06.332  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785237, pluginId: OneSpanSecureStorage, methodName: putString
[2025-06-04 15:59:57] 06-04 20:30:06.332  7808  7808 V Capacitor: callback: 785237, pluginId: OneSpanSecureStorage, methodName: putString, methodData: {"forKey":"biometricUsername","forValue":"91539937"}
[2025-06-04 15:59:58] 06-04 20:30:06.334  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785238, pluginId: OneSpanSecureStorage, methodName: putString
[2025-06-04 15:59:58] 06-04 20:30:06.335  7808  7808 V Capacitor: callback: 785238, pluginId: OneSpanSecureStorage, methodName: putString, methodData: {"forKey":"biometricPassword","forValue":"12312312314"}
[2025-06-04 15:59:58] 06-04 20:30:06.341  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785239, pluginId: OneSpanSecureStorage, methodName: write
[2025-06-04 15:59:59] 06-04 20:30:06.342  7808  7808 V Capacitor: callback: 785239, pluginId: OneSpanSecureStorage, methodName: write, methodData: {}
[2025-06-04 15:59:59] 06-04 20:30:06.344  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785240, pluginId: OneSpanSecureStorage, methodName: write
[2025-06-04 15:59:59] 06-04 20:30:06.344  7808  7808 V Capacitor: callback: 785240, pluginId: OneSpanSecureStorage, methodName: write, methodData: {}
[2025-06-04 15:59:59] 06-04 20:30:06.355  7808  7808 V Capacitor/Plugin: To native (Capacitor plugin): callbackId: 785241, pluginId: OneSpanSecureStorage, methodName: write
[2025-06-04 16:00:00] 06-04 20:30:06.356  7808  7808 V Capacitor: callback: 785241, pluginId: OneSpanSecureStorage, methodName: write, methodData: {}
[2025-06-04 16:00:00] 06-04 20:30:06.821  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 155 - Msg: entro utag.spa Triggerview => /home/<USER>/products
[2025-06-04 16:00:00] 06-04 20:30:06.828  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 156 - Msg: visitor :>>  [object Object]
[2025-06-04 16:00:01] 06-04 20:30:06.830  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 157 - Msg: visitAuth :>> {"id":"37/55698687234390BB","authState":1}
[2025-06-04 16:00:01] 06-04 20:30:06.885  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /home/<USER>/products
[2025-06-04 16:00:01] 06-04 20:30:06.885  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: Mutation Carga de Funciones.... IN  location.href = http://localhost/authentication/login
[2025-06-04 16:00:01] 06-04 20:30:06.885  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: inicio extensión PB Compra de cartera
[2025-06-04 16:00:02] 06-04 20:30:06.889  7808  7808 E Capacitor/Console: File: http://localhost/7423.ec3d02d0e1698874.js - Line 1 - Msg: SyntaxError: Identifier 'regexIsNumber_ADL' has already been declared
[2025-06-04 16:00:02] 06-04 20:30:06.889  7808  7808 E Capacitor: JavaScript Error: {"type":"js.error","error":{"message":"Uncaught SyntaxError: Identifier 'regexIsNumber_ADL' has already been declared","url":"http://localhost/authentication/login","line":1,"col":1,"errorObject":"{}"}}
[2025-06-04 16:00:02] 06-04 20:30:06.891  7808  7808 E Capacitor/Console: File: http://localhost/authentication/login - Line 1 - Msg: Uncaught SyntaxError: Identifier 'regexIsNumber_ADL' has already been declared
[2025-06-04 16:00:03] 06-04 20:30:06.894  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 551 - Msg: End BM Compra de Cartera - Carga de funciones
[2025-06-04 16:00:03] 06-04 20:30:06.894  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 551 - Msg: location changed carga de estilos => /home/<USER>/products
[2025-06-04 16:00:03] 06-04 20:30:06.894  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 879 - Msg: End load extensión PB Compra de Cartera - Carga de estilos
[2025-06-04 16:00:04] 06-04 20:30:06.896  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 907 - Msg: location changed carga de oferta => /home/<USER>/products
[2025-06-04 16:00:04] 06-04 20:30:06.902  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 924 - Msg: End... BM Compra de Cartera - Carga de Oferta
[2025-06-04 16:00:04] 06-04 20:30:06.902  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 2015 - Msg: location changed carga form1 => /home/<USER>/products
[2025-06-04 16:00:04] 06-04 20:30:06.902  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 2019 - Msg: Cargando form 1
[2025-06-04 16:00:05] 06-04 20:30:06.902  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 2138 - Msg: End BM Compra de Cartera-Carga Form 1
[2025-06-04 16:00:05] 06-04 20:30:06.903  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4165 - Msg: location changed carga form2 => /home/<USER>/products
[2025-06-04 16:00:05] 06-04 20:30:06.904  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4165 - Msg: Cargando form 2
[2025-06-04 16:00:06] 06-04 20:30:06.905  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 4177 - Msg: End BM Compra de Cartera - Carga Form 1
[2025-06-04 16:00:06] 06-04 20:30:06.907  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /home/<USER>/products
[2025-06-04 16:00:06] 06-04 20:30:06.907  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: Mutation Carga de Funciones.... IN  location.href = http://localhost/authentication/login
[2025-06-04 16:00:07] 06-04 20:30:06.908  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: inicio extensión PB Compra de cartera
[2025-06-04 16:00:07] 06-04 20:30:06.908  7808  7808 E Capacitor/Console: File: http://localhost/7423.ec3d02d0e1698874.js - Line 1 - Msg: SyntaxError: Identifier 'regexIsNumber_ADL' has already been declared
[2025-06-04 16:00:07] 06-04 20:30:06.911  7808  7808 E Capacitor: JavaScript Error: {"type":"js.error","error":{"message":"Uncaught SyntaxError: Identifier 'regexIsNumber_ADL' has already been declared","url":"http://localhost/authentication/login","line":1,"col":1,"errorObject":"{}"}}
[2025-06-04 16:00:07] 06-04 20:30:06.912  7808  7808 E Capacitor/Console: File: http://localhost/authentication/login - Line 1 - Msg: Uncaught SyntaxError: Identifier 'regexIsNumber_ADL' has already been declared
[2025-06-04 16:00:08] 06-04 20:30:06.913  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 551 - Msg: End BM Compra de Cartera - Carga de funciones
[2025-06-04 16:00:08] 06-04 20:30:06.914  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /home/<USER>/products
[2025-06-04 16:00:08] 06-04 20:30:06.915  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: Mutation Carga de Funciones.... IN  location.href = http://localhost/authentication/login
[2025-06-04 16:00:09] 06-04 20:30:06.916  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: inicio extensión PB Compra de cartera
[2025-06-04 16:00:09] 06-04 20:30:06.918  7808  7808 E Capacitor/Console: File: http://localhost/7423.ec3d02d0e1698874.js - Line 1 - Msg: SyntaxError: Identifier 'regexIsNumber_ADL' has already been declared
[2025-06-04 16:00:09] 06-04 20:30:06.920  7808  7808 E Capacitor: JavaScript Error: {"type":"js.error","error":{"message":"Uncaught SyntaxError: Identifier 'regexIsNumber_ADL' has already been declared","url":"http://localhost/authentication/login","line":1,"col":1,"errorObject":"{}"}}
[2025-06-04 16:00:09] 06-04 20:30:06.922  7808  7808 E Capacitor/Console: File: http://localhost/authentication/login - Line 1 - Msg: Uncaught SyntaxError: Identifier 'regexIsNumber_ADL' has already been declared
[2025-06-04 16:00:10] 06-04 20:30:06.923  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 551 - Msg: End BM Compra de Cartera - Carga de funciones
[2025-06-04 16:00:10] 06-04 20:30:06.925  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /home/<USER>/products
[2025-06-04 16:00:10] 06-04 20:30:06.926  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: Mutation Carga de Funciones.... IN  location.href = http://localhost/authentication/login
[2025-06-04 16:00:11] 06-04 20:30:06.927  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: inicio extensión PB Compra de cartera
[2025-06-04 16:00:11] 06-04 20:30:06.928  7808  7808 E Capacitor/Console: File: http://localhost/7423.ec3d02d0e1698874.js - Line 1 - Msg: SyntaxError: Identifier 'regexIsNumber_ADL' has already been declared
[2025-06-04 16:00:11] 06-04 20:30:06.930  7808  7808 E Capacitor: JavaScript Error: {"type":"js.error","error":{"message":"Uncaught SyntaxError: Identifier 'regexIsNumber_ADL' has already been declared","url":"http://localhost/authentication/login","line":1,"col":1,"errorObject":"{}"}}
[2025-06-04 16:00:11] 06-04 20:30:06.931  7808  7808 E Capacitor/Console: File: http://localhost/authentication/login - Line 1 - Msg: Uncaught SyntaxError: Identifier 'regexIsNumber_ADL' has already been declared
[2025-06-04 16:00:12] 06-04 20:30:06.931  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 551 - Msg: End BM Compra de Cartera - Carga de funciones
[2025-06-04 16:00:12] 06-04 20:30:06.932  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: location changed carga de funciones => /home/<USER>/products
[2025-06-04 16:00:12] 06-04 20:30:06.934  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: Mutation Carga de Funciones.... IN  location.href = http://localhost/authentication/login
[2025-06-04 16:00:13] 06-04 20:30:06.935  7808  7808 I Capacitor/Console: File: https://tags.tiqcdn.com/utag/adl/occidenteapp/qa/utag.js - Line 162 - Msg: inicio extensión PB Compra de cartera
[2025-06-04 16:00:13] 06-04 20:30:06.936  7808  7808 E Capacitor/Console: File: http://localhost/7423.ec3d02d0e1698874.js - Line 1 - Msg: SyntaxError: Identifier 'regexIsNumber_ADL' has already been declared
[2025-06-04 16:00:13] 06-04 20:30:06.939  7808  7808 E Capacitor: JavaScript Error: {"type":"js.error","error":{"message":"Uncaught SyntaxError: Identifier 'regexIsNumber_ADL' has already been declared","url":"http://localhost/authentication/login","line":1,"col":1,"errorObject":"{}"}}
