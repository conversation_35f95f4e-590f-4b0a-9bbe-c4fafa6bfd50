#!/bin/bash

# Script de Monitoreo de Deep Links
# Aplicación: Banco de Occidente Colombia

PACKAGE="com.grupoavaloc1.bancamovil"
LOG_FILE="deep_link_monitoring_$(date +%Y%m%d_%H%M%S).log"

# Colores
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

echo -e "${BLUE}=== INICIANDO MONITOREO DE DEEP LINKS ===${NC}"
echo -e "${YELLOW}Aplicación: $PACKAGE${NC}"
echo -e "${YELLOW}Log file: $LOG_FILE${NC}"
echo ""

# Obtener PID de la aplicación
get_app_pid() {
    adb shell ps -A | grep "$PACKAGE" | awk '{print $2}' | head -1
}

PID=$(get_app_pid)

if [ -z "$PID" ]; then
    echo -e "${RED}ERROR: La aplicación $PACKAGE no está ejecutándose${NC}"
    echo "Iniciando la aplicación..."
    adb shell monkey -p "$PACKAGE" -c android.intent.category.LAUNCHER 1 > /dev/null 2>&1
    sleep 3
    PID=$(get_app_pid)
    
    if [ -z "$PID" ]; then
        echo -e "${RED}ERROR: No se pudo iniciar la aplicación${NC}"
        exit 1
    fi
fi

echo -e "${GREEN}Aplicación encontrada con PID: $PID${NC}"
echo ""

# Función para procesar logs
process_log_line() {
    local line="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    # Escribir al archivo de log
    echo "[$timestamp] $line" >> "$LOG_FILE"
    
    # Procesar diferentes tipos de eventos
    if echo "$line" | grep -qi "appUrlOpen"; then
        echo -e "${GREEN}[DEEP LINK] $line${NC}"
    elif echo "$line" | grep -qi "deep.*link"; then
        echo -e "${YELLOW}[DEEP LINK] $line${NC}"
    elif echo "$line" | grep -qi "intent.*view"; then
        echo -e "${BLUE}[INTENT] $line${NC}"
    elif echo "$line" | grep -qi "url.*open"; then
        echo -e "${PURPLE}[URL OPEN] $line${NC}"
    elif echo "$line" | grep -qi "capacitor"; then
        echo -e "${YELLOW}[CAPACITOR] $line${NC}"
    elif echo "$line" | grep -qi "error\|exception"; then
        echo -e "${RED}[ERROR] $line${NC}"
    else
        echo "$line"
    fi
}

# Función de limpieza
cleanup() {
    echo ""
    echo -e "${YELLOW}Deteniendo monitoreo...${NC}"
    echo -e "${BLUE}Log guardado en: $LOG_FILE${NC}"
    exit 0
}

# Configurar trap para limpieza
trap cleanup SIGINT SIGTERM

echo -e "${GREEN}Iniciando monitoreo de logs (Ctrl+C para detener)...${NC}"
echo ""

# Monitorear logs en tiempo real
adb logcat --pid="$PID" | while IFS= read -r line; do
    # Filtrar líneas relevantes
    if echo "$line" | grep -qi "appUrlOpen\|deep.*link\|url.*open\|intent.*view\|capacitor\|onespan\|biometric\|error\|exception"; then
        process_log_line "$line"
    fi
done
