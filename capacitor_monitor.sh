#!/bin/bash

# Script específico para monitorear eventos de Capacitor
PACKAGE="com.grupoavaloc1.bancamovil"

echo "🔍 CAPACITOR EVENT MONITOR"
echo "=========================="

# Obtener PID de la app
PID=$(adb shell pidof $PACKAGE)

if [ -z "$PID" ]; then
    echo "❌ App no está corriendo. Iniciando..."
    adb shell monkey -p $PACKAGE -c android.intent.category.LAUNCHER 1
    sleep 3
    PID=$(adb shell pidof $PACKAGE)
fi

echo "📱 Monitoring PID: $PID"
echo "🔍 Filtrando eventos específicos de Capacitor..."
echo ""

# Limpiar logs
adb logcat -c

# Monitorear eventos específicos
echo "📊 LOGS EN TIEMPO REAL:"
echo "======================"

adb logcat --pid="$PID" | grep -E "(Capacitor|appUrlOpen|WebView|Console|JavaScript|cordova)" --line-buffered | while read line; do
    timestamp=$(date '+%H:%M:%S')
    echo "[$timestamp] $line"
done
